#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库模块 - 监听指定目录，解析不同格式文件内容，结构化存储到SQLite数据库
支持并发写入，文件类型可扩展

创建时间: 2025-01-25
"""

import configparser
import json
import logging
import os
import sqlite3
import time
import queue
import threading

from dateutil import parser
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from contextlib import contextmanager
from queue import Queue
from threading import Lock, Event
from typing import Dict, Any, Optional, List
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler


class DatabaseConnectionPool:
    """SQLite连接池管理器"""
    
    def __init__(self, db_path: str, max_connections: int = 10):
        self.db_path = db_path
        self.max_connections = max_connections
        self.connections = Queue(maxsize=max_connections)
        self.lock = Lock()
        self._initialize_pool()
        
    def _initialize_pool(self):
        """初始化连接池"""
        for _ in range(self.max_connections):
            conn = self._create_connection()
            if conn:
                self.connections.put(conn)
    
    def _create_connection(self) -> Optional[sqlite3.Connection]:
        """创建新的数据库连接"""
        try:
            conn = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=30.0
            )
            
            # 优化SQLite设置
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=-64000")
            conn.execute("PRAGMA temp_store=MEMORY")
            conn.execute("PRAGMA mmap_size=268435456")
            conn.execute("PRAGMA foreign_keys=ON")
            conn.execute("PRAGMA auto_vacuum=INCREMENTAL")
            conn.execute("PRAGMA busy_timeout=30000")
            
            conn.row_factory = sqlite3.Row
            return conn
            
        except Exception as e:
            logging.error(f"创建数据库连接失败: {e}")
            return None
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = None
        try:
            with self.lock:
                if not self.connections.empty():
                    conn = self.connections.get_nowait()
                else:
                    conn = self._create_connection()
                    
            if conn is None:
                raise Exception("无法获取数据库连接")
                
            yield conn
            
        except Exception as e:
            logging.error(f"数据库操作错误: {e}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                try:
                    conn.commit()
                    with self.lock:
                        if not self.connections.full():
                            self.connections.put(conn)
                        else:
                            conn.close()
                except Exception as e:
                    logging.error(f"归还连接时出错: {e}")
                    conn.close()
    
    def close_all(self):
        """关闭所有连接"""
        with self.lock:
            while not self.connections.empty():
                try:
                    conn = self.connections.get_nowait()
                    conn.close()
                except Exception as e:
                    logging.error(f"关闭连接时出错: {e}")

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, config_path: str = "config.ini"):
        # 首先初始化日志器
        self.logger = logging.getLogger('DatabaseManager')
        
        self.config = self._load_config(config_path)
        self.db_config = self.config['database']
        self.insert_sample_data = self.db_config.get('insert_sample_data',False)
        # 初始化数据库路径
        self.db_path = self.db_config.get('path', 'database.db')
        if not os.path.isabs(self.db_path):
            self.db_path = os.path.abspath(self.db_path)
        
        # 初始化连接池
        max_connections = int(self.db_config.get('max_connections', 10))
        self.connection_pool = DatabaseConnectionPool(self.db_path, max_connections)
        
        # 初始化数据库表
        self._initialize_database()
        
        self.logger.info(f"数据库管理器初始化完成: {self.db_path}")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        config = configparser.ConfigParser()
        config.read(config_path, encoding='utf-8')
        
        result = {}
        for section in config.sections():
            result[section] = dict(config[section])
        
        return result
    
    def _initialize_database(self):
        """初始化数据库表结构"""
        with self.connection_pool.get_connection() as conn:
            cursor = conn.cursor()
            
            # 创建源数据表，保存数据的输入信息-插入操作
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS origional_table (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sat_id TEXT,
                        sat_category TEXT,
                        sat_load_category TEXT,
                        planed_start_time TEXT,
                        planed_end_time TEXT,
                        file_size REAL,
                        file_name TEXT,
                        file_path TEXT,
                        image_truth_width REAL,
                        image_resolution REAL,
                        timestamp TEXT NOT NULL,
                        enu_base_longitude REAL,
                        enu_base_latitude REAL,
                        enu_base_altitude REAL,
                        sat_enu_x REAL,
                        sat_enu_y REAL,
                        sat_enu_z REAL,
                        cam_enu_x REAL,
                        cam_enu_y REAL,
                        cam_enu_z REAL,
                        cam_enu_w REAL,
                        sat_j2000_x REAL,
                        sat_j2000_y REAL,
                        sat_j2000_z REAL,
                        sat_qbi_x REAL,
                        sat_qbi_y REAL,
                        sat_qbi_z REAL,
                        sat_qbi_w REAL,
                        load_fov_x REAL,
                        load_fov_y REAL,
                        image_pixel_x REAL,
                        image_pixel_y REAL                    
                    )
                ''')

            # 创建真值表-插入操作
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS truth_table (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sat_id TEXT,
                    timestamp TEXT NOT NULL,
                        target_id TEXT,
                        fleet_number TEXT,
                        target_category TEXT,
                        target_direction TEXT,
                        target_longitude REAL,
                        target_latitude REAL,
                        target_altitude REAL,
                        target_sat_z REAL,
                        target_sat_y REAL,
                        target_sat_x REAL,
                        target_x REAL,
                        target_y REAL,
                        target_width REAL,
                        target_height REAL
                    )
                ''')

            # 创建特征表，保存算法的中间结果信息--插入+更新操作
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS feature_table (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sat_id TEXT NOT NULL,
                        fleet_number TEXT,
                        target_category TEXT,
                        target_id TEXT,
                        timestamp TEXT NOT NULL,
                        target_image_path TEXT,
                        target_truth BOOL,
                        target_x REAL,
                        target_y REAL,
                        target_width REAL,
                        target_height REAL,
                        target_longitude REAL,
                        target_latitude REAL,
                        target_fusion_longitude REAL,
                        target_fusion_latitude REAL,
                        target_longitude_speed REAL,
                        target_latitude_speed REAL,
                        target_total_speed REAL,
                        target_npy_path TEXT
                    )
                ''')

            # 创建决策表，保存数据的决策信息--只有插入操作
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS decision_table (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT,
                        query_days TEXT,
                        fleet_number TEXT,
                        speed_threat REAL,
                        heading_threat REAL,
                        distance_threat REAL,
                        heading_angle REAL,
                        speed REAL,
                        lat REAL,
                        lon REAL,
                        intention TEXT
                    )
                ''')

            # 创建索引以提高查询性能
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_origional_timestamp ON origional_table(timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_origional_sat_id ON origional_table(sat_id)')

            cursor.execute('CREATE INDEX IF NOT EXISTS idx_truth_sat_id ON truth_table(sat_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_truth_timestamp ON truth_table(timestamp)')
            
            conn.commit()
            if self.insert_sample_data:
                self.insert_origin_sample_data()
                self.insert_truth_sample_data()
                self.insert_feature_sample_data()
                self.insert_decision_random_sample_data()

            self.logger.info("数据库表结构初始化完成")
    
    def insert_origin_sample_data(self, num_records=5):
        """向origional_table插入随机生成的示例数据"""
        try:
            import random
            from datetime import datetime, timedelta, timezone
            
            device_type_map = {0: 'GEO', 131072: 'LEO', 131073: 'LEO_SAR', 131074: 'LEO_RADAR', 131075: 'LEO_ELECTRO'}
            equip_type_map = {1: 'CCD', 2: 'IR', 3: 'SAR', 4: 'RADAR', 5: 'ELECTRO', 6: 'PAN'}
            
            success_count = 0
            failed_count = 0
            
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                for i in range(num_records):
                    try:
                        # 生成随机时间戳（过去30天内）
                        base_time = datetime.now(timezone.utc)
                        random_days = random.randint(0, 30)
                        random_hours = random.randint(0, 23)
                        random_minutes = random.randint(0, 59)
                        
                        random_time = base_time - timedelta(
                            days=random_days, 
                            hours=random_hours, 
                            minutes=random_minutes
                        )
                        
                        # 转换为UTC毫秒级时间戳
                        timestamp_ms = str(int(random_time.timestamp() * 1000))
                        
                        # 生成随机卫星数据
                        origin_data = {
                            'sat_id': f'SAT_{random.randint(1000, 9999)}',
                            'sat_category': random.choice(list(device_type_map.values())),
                            'sat_load_category': random.choice(list(equip_type_map.values())),
                            'planed_start_time': str(int(random_time.timestamp()) - 3600),
                            'planed_end_time': str(int(random_time.timestamp()) + 3600),
                            'timestamp': timestamp_ms,
                            'file_size': round(random.uniform(10.0, 1000.0), 2),
                            'file_name': f'image_{timestamp_ms}.jpg',
                            'file_path': f'/data/images/{timestamp_ms}.jpg',
                            'image_truth_width': round(random.uniform(100.0, 1000.0), 2),
                            'image_resolution': round(random.uniform(0.1, 10.0), 2),
                            'enu_base_longitude': round(random.uniform(-180.0, 180.0), 6),
                            'enu_base_latitude': round(random.uniform(-90.0, 90.0), 6),
                            'enu_base_altitude': round(random.uniform(0.0, 10000.0), 2),
                            'sat_enu_x': round(random.uniform(-1000000.0, 1000000.0), 2),
                            'sat_enu_y': round(random.uniform(-1000000.0, 1000000.0), 2),
                            'sat_enu_z': round(random.uniform(-1000000.0, 1000000.0), 2),
                            'cam_enu_x': round(random.uniform(-1.0, 1.0), 6),
                            'cam_enu_y': round(random.uniform(-1.0, 1.0), 6),
                            'cam_enu_z': round(random.uniform(-1.0, 1.0), 6),
                            'cam_enu_w': round(random.uniform(-1.0, 1.0), 6),
                            'sat_j2000_x': round(random.uniform(-1000000.0, 1000000.0), 2),
                            'sat_j2000_y': round(random.uniform(-1000000.0, 1000000.0), 2),
                            'sat_j2000_z': round(random.uniform(-1000000.0, 1000000.0), 2),
                            'sat_qbi_x': round(random.uniform(-1.0, 1.0), 6),
                            'sat_qbi_y': round(random.uniform(-1.0, 1.0), 6),
                            'sat_qbi_z': round(random.uniform(-1.0, 1.0), 6),
                            'sat_qbi_w': round(random.uniform(-1.0, 1.0), 6),
                            'load_fov_x': round(random.uniform(1.0, 90.0), 2),
                            'load_fov_y': round(random.uniform(1.0, 90.0), 2),
                            'image_pixel_x': random.randint(1000, 4000),
                            'image_pixel_y': random.randint(1000, 4000)
                        }
                        
                        # 插入数据
                        record_id = self.insert_origin_data(origin_data)
                        if record_id:
                            success_count += 1
                        else:
                            failed_count += 1
                            
                    except Exception as e:
                        failed_count += 1
                        self.logger.error(f"插入源示例数据失败: {e}")
                
                conn.commit()
            
            self.logger.info(f"源示例数据插入完成: 成功 {success_count} 条, 失败 {failed_count} 条")
            return success_count
            
        except Exception as e:
            self.logger.error(f"插入源示例数据总体失败: {e}")
            return 0

    def insert_truth_sample_data(self, num_records=10):
        """向truth_table插入随机生成的示例数据"""
        try:
            import random
            from datetime import datetime, timedelta, timezone
            
            target_type_map = {
                101: 'AIRPORT', 102: 'PORT', 1201: 'CARRIER', 1202: 'BATTLESHIP', 
                1203: 'DESTROYER', 1204: 'SUPPLY', 1205: 'FRIGATE'
            }
            
            # 获取现有的卫星ID和时间戳用于关联
            sat_ids = []
            timestamps = []
            
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT DISTINCT sat_id, timestamp FROM origional_table LIMIT 10")
                rows = cursor.fetchall()
                for row in rows:
                    sat_ids.append(row['sat_id'])
                    timestamps.append(row['timestamp'])
            
            # 如果没有源数据，创建一些虚拟数据
            if not sat_ids:
                sat_ids = [f'SAT_{random.randint(1000, 9999)}' for _ in range(3)]
                base_time = datetime.now(timezone.utc)
                timestamps = [str(int((base_time - timedelta(days=i)).timestamp() * 1000)) for i in range(3)]
            
            success_count = 0
            failed_count = 0
            
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                for i in range(num_records):
                    try:
                        # 随机选择卫星ID和时间戳
                        sat_id = random.choice(sat_ids)
                        timestamp = random.choice(timestamps)
                        
                        # 生成随机目标数据
                        truth_data = {
                            'sat_id': sat_id,
                            'timestamp': timestamp,
                            'target_id': f'TARGET_{random.randint(10000, 99999)}',
                            'fleet_number': f'FLEET_{random.randint(1, 5):02d}',
                            'target_category': random.choice(list(target_type_map.values())),
                            'target_direction': str(round(random.uniform(0.0, 360.0), 1)),
                            'target_longitude': round(random.uniform(-180.0, 180.0), 6),
                            'target_latitude': round(random.uniform(-90.0, 90.0), 6),
                            'target_altitude': round(random.uniform(0.0, 10000.0), 2),
                            'target_sat_x': round(random.uniform(-1000.0, 1000.0), 2),
                            'target_sat_y': round(random.uniform(-1000.0, 1000.0), 2),
                            'target_sat_z': round(random.uniform(-1000.0, 1000.0), 2),
                            'target_x': round(random.uniform(0.0, 1000.0), 2),
                            'target_y': round(random.uniform(0.0, 1000.0), 2),
                            'target_width': round(random.uniform(10.0, 100.0), 2),
                            'target_height': round(random.uniform(10.0, 100.0), 2)
                        }
                        
                        # 插入数据
                        record_id = self.insert_truth_data(truth_data)
                        if record_id:
                            success_count += 1
                        else:
                            failed_count += 1
                            
                    except Exception as e:
                        failed_count += 1
                        self.logger.error(f"插入真值示例数据失败: {e}")
                
                conn.commit()
            
            self.logger.info(f"真值示例数据插入完成: 成功 {success_count} 条, 失败 {failed_count} 条")
            return success_count
            
        except Exception as e:
            self.logger.error(f"插入真值示例数据总体失败: {e}")
            return 0

    def insert_feature_sample_data(self, num_records=15):
        """向feature_table插入随机生成的示例数据"""
        try:
            import random
            from datetime import datetime, timedelta, timezone
            
            target_type_map = {
                101: 'AIRPORT', 102: 'PORT', 1201: 'CARRIER', 1202: 'BATTLESHIP', 
                1203: 'DESTROYER', 1204: 'SUPPLY', 1205: 'FRIGATE'
            }
            
            # 获取现有的目标ID用于关联
            target_ids = []
            sat_ids = []
            timestamps = []
            
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT DISTINCT target_id, sat_id, timestamp FROM truth_table LIMIT 10")
                rows = cursor.fetchall()
                for row in rows:
                    target_ids.append(row['target_id'])
                    sat_ids.append(row['sat_id'])
                    timestamps.append(row['timestamp'])
            
            # 如果没有真值数据，创建一些虚拟数据
            if not target_ids:
                target_ids = [f'TARGET_{random.randint(10000, 99999)}' for _ in range(5)]
                sat_ids = [f'SAT_{random.randint(1000, 9999)}' for _ in range(3)]
                base_time = datetime.now(timezone.utc)
                timestamps = [str(int((base_time - timedelta(days=i)).timestamp() * 1000)) for i in range(3)]
            
            success_count = 0
            failed_count = 0
            
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                for i in range(num_records):
                    try:
                        # 随机选择目标ID、卫星ID和时间戳
                        target_id = random.choice(target_ids)
                        sat_id = random.choice(sat_ids)
                        # timestamp = random.choice(timestamps)
                        timestamp = 111 + i
                        # 生成随机特征数据
                        feature_data = {
                            'sat_id': sat_id,
                            'target_id': target_id,
                            'timestamp': timestamp,
                            'fleet_number': f'FLEET_{random.randint(1, 5):02d}',
                            'target_category': random.choice(list(target_type_map.values())),
                            'target_image_path': f'/data/targets/{target_id}_{timestamp}.jpg',
                            'target_truth': random.choice([True, False]),
                            'target_x': round(random.uniform(0.0, 1000.0), 2),
                            'target_y': round(random.uniform(0.0, 1000.0), 2),
                            'target_width': round(random.uniform(10.0, 100.0), 2),
                            'target_height': round(random.uniform(10.0, 100.0), 2),
                            'target_longitude': round(random.uniform(-180.0, 180.0), 6),
                            'target_latitude': round(random.uniform(-90.0, 90.0), 6),
                            'target_fusion_longitude': round(random.uniform(-180.0, 180.0), 6),
                            'target_fusion_latitude': round(random.uniform(-90.0, 90.0), 6),
                            'target_longitude_speed': round(random.uniform(-10.0, 10.0), 2),
                            'target_latitude_speed': round(random.uniform(-10.0, 10.0), 2),
                            'target_total_speed': round(random.uniform(0.0, 20.0), 2),
                            'target_npy_path': f'/data/features/{target_id}_{timestamp}.npy'
                        }
                        
                        # 插入数据
                        record_id = self.insert_feature_data(feature_data)
                        if record_id:
                            success_count += 1
                        else:
                            failed_count += 1
                            
                    except Exception as e:
                        failed_count += 1
                        self.logger.error(f"插入特征示例数据失败: {e}")
                
                conn.commit()
            
            self.logger.info(f"特征示例数据插入完成: 成功 {success_count} 条, 失败 {failed_count} 条")
            return success_count
            
        except Exception as e:
            self.logger.error(f"插入特征示例数据总体失败: {e}")
            return 0
    
    def insert_decision_random_sample_data(self, num_records=10):
        """向decision_table插入随机生成的示例数据
        
        :param num_records: 要插入的记录数量，默认为10
        :return: 成功插入的记录数量
        """
        try:
            import random
            import time
            from datetime import datetime, timedelta, timezone
            
            # 当前时间作为基准
            base_time = datetime.now(timezone.utc)
            
            # 可能的舰队编号和意图
            fleet_numbers = ['FLEET_001', 'FLEET_002', 'FLEET_003', 'FLEET_004', 'FLEET_005']
            intentions = ['正常', '可疑', '高度可疑', '威胁', '未知']
            query_days_options = ['1', '3', '5', '7', '10', '14', '30']
            
            success_count = 0
            failed_count = 0

            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                for i in range(num_records):
                    try:
                        # 生成随机时间戳（过去30天内）
                        random_days = random.randint(0, 30)
                        random_hours = random.randint(0, 23)
                        random_minutes = random.randint(0, 59)
                        
                        random_time = base_time - timedelta(
                            days=random_days, 
                            hours=random_hours, 
                            minutes=random_minutes
                        )
                        
                        # 转换为UTC毫秒级时间戳
                        # timestamp_ms = str(int(random_time.timestamp() * 1000))
                        timestamp_ms = 111 + i
                        self.logger.info(f"时间戳为：{timestamp_ms}")
                        # 生成随机数据
                        data = {
                            'timestamp': timestamp_ms,
                            'query_days': random.choice(query_days_options),
                            'fleet_number': random.choice(fleet_numbers),
                            'speed_threat': round(random.uniform(0.0, 1.0), 2),
                            'heading_threat': round(random.uniform(0.0, 1.0), 2),
                            'distance_threat': round(random.uniform(0.0, 1.0), 2),
                            'heading_angle': round(random.uniform(0.0, 360.0), 1),
                            'speed': round(random.uniform(5.0, 40.0), 1),
                            'lat': round(random.uniform(-90.0, 90.0), 6),
                            'lon': round(random.uniform(-180.0, 180.0), 6),
                            'intention': random.choice(intentions)
                        }
                        
                        # 构建插入SQL
                        columns = list(data.keys())
                        values = list(data.values())
                        placeholders = ', '.join(['?'] * len(columns))
                        
                        insert_sql = f"""
                            INSERT INTO decision_table ({', '.join(columns)})
                            VALUES ({placeholders})
                        """
                        
                        cursor.execute(insert_sql, values)
                        success_count += 1
                        
                        # 每插入10条记录记录一次日志
                        if (i + 1) % 10 == 0:
                            self.logger.debug(f"已插入 {i + 1} 条决策示例数据")
                            
                    except Exception as e:
                        failed_count += 1
                        self.logger.error(f"插入决策示例数据失败: {e}")
                
                conn.commit()
            
            self.logger.info(f"决策示例数据插入完成: 成功 {success_count} 条, 失败 {failed_count} 条")
            return success_count
            
        except Exception as e:
            self.logger.error(f"插入决策示例数据总体失败: {e}")
            return 0
        
    def insert_origin_data(self, data: Dict[str, Any]) -> Optional[int]:
        """插入源数据
        
        :param data: 源数据字典，可包含任意origional_table的字段
        :return: 插入记录的ID，失败返回None
        """
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                # 定义origional_table的所有列（除了自增主键），匹配实际表结构
                all_columns = {
                    'sat_id': 'TEXT',
                    'sat_category': 'TEXT',
                    'sat_load_category': 'TEXT',
                    'planed_start_time': 'TEXT',
                    'planed_end_time': 'TEXT',
                    'file_size': 'REAL',
                    'file_name': 'TEXT',
                    'file_path': 'TEXT',
                    'image_truth_width': 'REAL',
                    'image_resolution': 'REAL',
                    'timestamp': 'TEXT NOT NULL',
                    'enu_base_longitude': 'REAL',
                    'enu_base_latitude': 'REAL',
                    'enu_base_altitude': 'REAL',
                    'sat_enu_x': 'REAL',
                    'sat_enu_y': 'REAL',
                    'sat_enu_z': 'REAL',
                    'cam_enu_x': 'REAL',
                    'cam_enu_y': 'REAL',
                    'cam_enu_z': 'REAL',
                    'cam_enu_w': 'REAL',
                    'sat_j2000_x': 'REAL',
                    'sat_j2000_y': 'REAL',
                    'sat_j2000_z': 'REAL',
                    'sat_qbi_x': 'REAL',
                    'sat_qbi_y': 'REAL',
                    'sat_qbi_z': 'REAL',
                    'sat_qbi_w': 'REAL',
                    'load_fov_x': 'REAL',
                    'load_fov_y': 'REAL',
                    'image_pixel_x': 'REAL',
                    'image_pixel_y': 'REAL'
                }
                
                # 检查必须字段
                required_fields = ['sat_id', 'timestamp']
                missing_required = [field for field in required_fields if not data.get(field)]
                if missing_required:
                    raise ValueError(f"缺少必须字段: {missing_required}")
                
                # 只包含数据中存在且在表结构中的列
                columns_to_insert = []
                values_to_insert = []
                
                for column, value in data.items():
                    if column in all_columns and value is not None:
                        columns_to_insert.append(column)
                        # 处理特殊类型
                        if 'INTEGER' in all_columns[column]:
                            # 整型处理
                            try:
                                values_to_insert.append(int(float(str(value))) if value != '' else None)
                            except (ValueError, TypeError):
                                values_to_insert.append(None)
                        elif 'REAL' in all_columns[column]:
                            # 浮点型处理
                            try:
                                values_to_insert.append(float(value) if value != '' else None)
                            except (ValueError, TypeError):
                                values_to_insert.append(None)
                        else:
                            # 文本类型处理
                            values_to_insert.append(str(value) if value != '' else None)
                
                # 检查是否有数据要插入
                if not columns_to_insert:
                    raise ValueError("没有有效的数据列可以插入")
                
                # 构建动态SQL语句
                placeholders = ', '.join(['?'] * len(columns_to_insert))
                insert_sql = f"""
                    INSERT INTO origional_table ({', '.join(columns_to_insert)})
                    VALUES ({placeholders})
                """
                
                # 记录调试信息
                self.logger.debug(f"插入源数据: {len(columns_to_insert)}个字段 - {columns_to_insert}")
                
                cursor.execute(insert_sql, values_to_insert)
                conn.commit()
                
                record_id = cursor.lastrowid
                self.logger.debug(f"成功插入源数据，记录ID: {record_id}")
                return record_id
                    
        except Exception as e:
            self.logger.error(f"插入源数据失败: {e}")
            return None
    
    def insert_truth_data(self, data: Dict[str, Any]) -> Optional[int]:
        """插入真值数据"""
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                # 定义truth_table的所有列（除了自增主键），匹配实际表结构
                all_columns = {
                    'sat_id': 'TEXT',
                    'timestamp': 'TEXT NOT NULL',
                    'target_id': 'TEXT',
                    'fleet_number': 'TEXT',
                    'target_category': 'TEXT',
                    'target_direction': 'TEXT',
                    'target_longitude': 'REAL',
                    'target_latitude': 'REAL',
                    'target_altitude': 'REAL',
                    'target_sat_z': 'REAL',
                    'target_sat_y': 'REAL',
                    'target_sat_x': 'REAL',
                    'target_x': 'REAL',
                    'target_y': 'REAL',
                    'target_width': 'REAL',
                    'target_height': 'REAL'
                }
                
                # 检查必须字段
                required_fields = ['sat_id', 'timestamp']
                missing_required = [field for field in required_fields if not data.get(field)]
                if missing_required:
                    raise ValueError(f"缺少必须字段: {missing_required}")
                
                # 只包含数据中存在且在表结构中的列
                columns_to_insert = []
                values_to_insert = []
                
                for column, value in data.items():
                    if column in all_columns and value is not None:
                        columns_to_insert.append(column)
                        # 处理特殊类型
                        if 'REAL' in all_columns[column]:
                            # 浮点型处理
                            try:
                                values_to_insert.append(float(value) if value != '' else None)
                            except (ValueError, TypeError):
                                values_to_insert.append(None)
                        else:
                            # 文本类型处理
                            values_to_insert.append(str(value) if value != '' else None)
                
                # 检查是否有数据要插入
                if not columns_to_insert:
                    raise ValueError("没有有效的数据列可以插入")
                
                # 构建动态SQL语句
                placeholders = ', '.join(['?'] * len(columns_to_insert))
                insert_sql = f"""
                    INSERT INTO truth_table ({', '.join(columns_to_insert)})
                    VALUES ({placeholders})
                """
                
                # 记录调试信息
                self.logger.debug(f"插入真值数据: {len(columns_to_insert)}个字段 - {columns_to_insert}")
                
                cursor.execute(insert_sql, values_to_insert)
                conn.commit()
                
                record_id = cursor.lastrowid
                self.logger.debug(f"成功插入真值数据，记录ID: {record_id}")
                return record_id
                
        except Exception as e:
            self.logger.error(f"插入真值数据失败: {e}")
    
    def insert_feature_data(self, data: Dict[str, Any]) -> Optional[int]:
        """插入特征数据"""
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                # 定义feature_table的所有列（除了自增主键）
                all_columns = {
                    'sat_id': 'TEXT NOT NULL',
                    'fleet_number': 'TEXT',
                    'target_category': 'TEXT',
                    'target_id': 'TEXT',
                    'timestamp': 'TEXT NOT NULL',
                    'target_image_path': 'TEXT',
                    'target_truth': 'BOOL',
                    'target_x': 'REAL',
                    'target_y': 'REAL',
                    'target_width': 'REAL',
                    'target_height': 'REAL',
                    'target_longitude': 'REAL',
                    'target_latitude': 'REAL',
                    'target_fusion_longitude': 'REAL',
                    'target_fusion_latitude': 'REAL',
                    'target_longitude_speed': 'REAL',
                    'target_latitude_speed': 'REAL',
                    'target_total_speed': 'REAL',
                    'target_npy_path': 'TEXT',
                    'target_json_path': 'TEXT'
                }
                
                # 检查必须字段
                required_fields = ['sat_id', 'timestamp']
                missing_required = [field for field in required_fields if not data.get(field)]
                if missing_required:
                    raise ValueError(f"缺少必须字段: {missing_required}")
                
                # 只包含数据中存在且在表结构中的列
                columns_to_insert = []
                values_to_insert = []
                
                for column, value in data.items():
                    if column in all_columns and value is not None:
                        columns_to_insert.append(column)
                        # 处理特殊类型
                        if all_columns[column] == 'BOOL':
                            # 布尔值处理
                            if isinstance(value, bool):
                                values_to_insert.append(value)
                            elif isinstance(value, str):
                                values_to_insert.append(value.lower() in ['true', '1', 'yes', 'real'])
                            else:
                                values_to_insert.append(bool(value))
                        elif 'INTEGER' in all_columns[column]:
                            # 整型处理
                            try:
                                values_to_insert.append(int(float(str(value))) if value != '' else None)
                            except (ValueError, TypeError):
                                values_to_insert.append(None)
                        elif 'REAL' in all_columns[column]:
                            # 浮点型处理
                            try:
                                values_to_insert.append(float(value) if value != '' else None)
                            except (ValueError, TypeError):
                                values_to_insert.append(None)
                        else:
                            # 文本类型处理
                            values_to_insert.append(str(value) if value != '' else None)
                
                # 检查是否有数据要插入
                if not columns_to_insert:
                    raise ValueError("没有有效的数据列可以插入")
                
                # 构建动态SQL语句
                placeholders = ', '.join(['?'] * len(columns_to_insert))
                insert_sql = f"""
                    INSERT INTO feature_table ({', '.join(columns_to_insert)})
                    VALUES ({placeholders})
                """
                
                # 记录调试信息
                self.logger.debug(f"插入特征数据: {len(columns_to_insert)}个字段 - {columns_to_insert}")
                
                cursor.execute(insert_sql, values_to_insert)
                conn.commit()
                
                record_id = cursor.lastrowid
                self.logger.debug(f"成功插入特征数据，记录ID: {record_id}")
                return record_id
        except Exception as e:
            self.logger.error(f"插入特征数据失败: {e}")
    
    def insert_decision_data(self, data: Dict[str, Any]) -> Optional[int]:
        """插入决策数据"""
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                all_columns = {
                    'timestamp': 'TEXT',
                    'query_days': 'TEXT',
                    'fleet_number': 'TEXT',
                    'speed_threat': 'REAL',
                    'heading_threat': 'REAL',
                    'distance_threat': 'REAL',
                    'heading_angle': 'REAL',
                    'speed': 'REAL',
                    'lat': 'REAL',
                    'lon': 'REAL',
                    'intention': 'TEXT'
                }
                
                # 检查必须字段
                required_fields = ['timestamp', 'fleet_number']
                missing_required = [field for field in required_fields if not data.get(field)]
                if missing_required:
                    raise ValueError(f"缺少必须字段: {missing_required}")
                
                # 只包含数据中存在且在表结构中的列
                columns_to_insert = []
                values_to_insert = []
                
                for column, value in data.items():
                    if column in all_columns and value is not None:
                        columns_to_insert.append(column)
                        # 处理特殊类型
                        if 'INTEGER' in all_columns[column]:
                            # 整型处理
                            try:
                                values_to_insert.append(int(float(str(value))) if value != '' else None)
                            except (ValueError, TypeError):
                                values_to_insert.append(None)
                        elif 'REAL' in all_columns[column]:
                            # 浮点型处理
                            try:
                                values_to_insert.append(float(value) if value != '' else None)
                            except (ValueError, TypeError):
                                values_to_insert.append(None)
                        else:
                            # 文本类型处理
                            values_to_insert.append(str(value) if value != '' else None)
                
                # 检查是否有数据要插入
                if not columns_to_insert:
                    raise ValueError("没有有效的数据列可以插入")
                
                # 构建动态SQL语句
                placeholders = ', '.join(['?'] * len(columns_to_insert))
                insert_sql = f"""
                    INSERT INTO decision_table ({', '.join(columns_to_insert)})
                    VALUES ({placeholders})
                """
                
                # 记录调试信息
                self.logger.debug(f"插入决策数据: {len(columns_to_insert)}个字段 - {columns_to_insert}")
                
                cursor.execute(insert_sql, values_to_insert)
                conn.commit()
                
                record_id = cursor.lastrowid
                self.logger.debug(f"成功插入决策数据，记录ID: {record_id}")
                return record_id
                    
        except Exception as e:
            self.logger.error(f"插入决策数据失败: {e}")
    
    def update_WZZH(self, sat_id: str, timestamp: str, target_id: str, longitude: float, latitude: float) -> bool:
        """
        更新位置转换(WZZH)算法结果到feature_table
        
        :param sat_id: 卫星ID
        :param timestamp: 时间戳
        :param target_id: 目标ID
        :param longitude: 经度
        :param latitude: 纬度
        :return: 更新是否成功
        """
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查记录是否存在
                cursor.execute("""
                    SELECT id FROM feature_table 
                    WHERE sat_id = ? AND timestamp = ? AND target_id = ?
                """, (sat_id, timestamp, target_id))
                
                existing_record = cursor.fetchone()
                
                if existing_record:
                    # 更新现有记录
                    update_sql = """
                        UPDATE feature_table 
                        SET target_longitude = ?, target_latitude = ?
                        WHERE sat_id = ? AND timestamp = ? AND target_id = ?
                    """
                    
                    cursor.execute(update_sql, (longitude, latitude, sat_id, timestamp, target_id))
                    conn.commit()
                    
                    self.logger.info(f"WZZH更新位置信息成功: target_id={target_id}, "
                                   f"lat={latitude}, lon={longitude} (ID: {existing_record['id']})")
                    return True
                else:
                    # 插入新记录
                    feature_data = {
                        'sat_id': sat_id,
                        'target_id': target_id,
                        'timestamp': timestamp,
                        'target_longitude': longitude,
                        'target_latitude': latitude
                    }
                    
                    record_id = self.insert_feature_data(feature_data)
                    if record_id:
                        self.logger.info(f"WZZH插入新的位置记录: target_id={target_id}, "
                                       f"lat={latitude}, lon={longitude} (ID: {record_id})")
                        return True
                    else:
                        self.logger.error(f"WZZH插入新记录失败: target_id={target_id}")
                        return False
                        
        except Exception as e:
            self.logger.error(f"WZZH更新位置信息失败: {e}")
            return False

    def update_RHDW(self, sat_id: str, timestamp: str, target_id: str, fusion_longitude: float, 
                    fusion_latitude: float, longitude_speed: float = None, latitude_speed: float = None, 
                    total_speed: float = None, target_category: str = None) -> bool:
        """
        更新融合定位(RHDW)算法结果到feature_table
        
        :param sat_id: 卫星ID
        :param timestamp: 时间戳
        :param target_id: 目标ID
        :param fusion_longitude: 融合经度
        :param fusion_latitude: 融合纬度
        :param longitude_speed: 经度速度
        :param latitude_speed: 纬度速度
        :param total_speed: 总速度
        :param target_category: 目标类别
        :return: 更新是否成功
        """
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查记录是否存在
                cursor.execute("""
                    SELECT id FROM feature_table 
                    WHERE sat_id = ? AND timestamp = ? AND target_id = ?
                """, (sat_id, timestamp, target_id))
                
                existing_record = cursor.fetchone()
                
                if existing_record:
                    # 构建动态更新SQL
                    update_fields = ["target_fusion_longitude = ?", "target_fusion_latitude = ?"]
                    update_values = [fusion_longitude, fusion_latitude]
                    
                    if longitude_speed is not None:
                        update_fields.append("target_longitude_speed = ?")
                        update_values.append(longitude_speed)
                    
                    if latitude_speed is not None:
                        update_fields.append("target_latitude_speed = ?")
                        update_values.append(latitude_speed)
                    
                    if total_speed is not None:
                        update_fields.append("target_total_speed = ?")
                        update_values.append(total_speed)
                    
                    if target_category is not None:
                        update_fields.append("target_category = ?")
                        update_values.append(target_category)
                    
                    # 添加WHERE条件的值
                    update_values.extend([sat_id, timestamp, target_id])
                    
                    update_sql = f"""
                        UPDATE feature_table 
                        SET {', '.join(update_fields)}
                        WHERE sat_id = ? AND timestamp = ? AND target_id = ?
                    """
                    
                    cursor.execute(update_sql, update_values)
                    conn.commit()
                    
                    self.logger.info(f"TSFX更新融合位置信息成功: target_id={target_id}, "
                                   f"fusion_lon={fusion_longitude}, fusion_lat={fusion_latitude}, "
                                   f"total_speed={total_speed} (ID: {existing_record['id']})")
                    return True
                else:
                    # 插入新记录
                    feature_data = {
                        'sat_id': sat_id,
                        'target_id': target_id,
                        'timestamp': timestamp,
                        'target_fusion_longitude': fusion_longitude,
                        'target_fusion_latitude': fusion_latitude,
                        'target_truth': True
                    }
                    
                    # 添加可选字段
                    if longitude_speed is not None:
                        feature_data['target_longitude_speed'] = longitude_speed
                    if latitude_speed is not None:
                        feature_data['target_latitude_speed'] = latitude_speed
                    if total_speed is not None:
                        feature_data['target_total_speed'] = total_speed
                    if target_category is not None:
                        feature_data['target_category'] = target_category
                    
                    record_id = self.insert_feature_data(feature_data)
                    if record_id:
                        self.logger.info(f"TSFX插入新的融合位置记录: target_id={target_id}, "
                                       f"fusion_lon={fusion_longitude}, fusion_lat={fusion_latitude}, "
                                       f"total_speed={total_speed} (ID: {record_id})")
                        return True
                    else:
                        self.logger.error(f"TSFX插入新记录失败: target_id={target_id}")
                        return False
                        
        except Exception as e:
            self.logger.error(f"TSFX更新融合位置信息失败: {e}")
            return False
    
    def close(self):
        """关闭数据库连接池"""
        self.connection_pool.close_all()
        self.logger.info("数据库连接池已关闭")

class FileWatcher:
    """文件监控器 - 使用生产者消费者模式"""
    
    def __init__(self, database_manager: DatabaseManager, config: Dict[str, Any]):
        self.db_manager = database_manager
        self.config = config
        self.db_config = config['database']
        
        # 支持多个监控目录，配置项为 watch_directories（分号分隔）
        self.watch_directories = self.db_config.get('watch_directories', './data/watch')
        self.observer = None
        
        # 消息队列和消费者线程
        self.file_queue = queue.Queue(maxsize=1000)  # 设置队列最大大小防止内存溢出
        self.consumer_threads = []
        self.stop_event = Event()
        
        # 消费者线程数量
        self.num_consumers = int(self.db_config.get('consumer_threads', 3))
        
        self.logger = logging.getLogger('FileWatcher')
        
        # 确保监控目录存在
        try:
            # 如果从配置读取的是分号分隔的字符串，转换为列表
            if isinstance(self.watch_directories, str):
                self.watch_directories = [p.strip() for p in self.watch_directories.split(';') if p.strip()]
            # 逐个创建目录
            for p in self.watch_directories:
                os.makedirs(p, exist_ok=True)
        except Exception as e:
            self.logger.error(f"创建监控目录失败: {e}")
            raise
    
    class FileEventHandler(FileSystemEventHandler):
        """文件事件处理器 - 生产者"""
        
        def __init__(self, file_queue):
            self.file_queue = file_queue
            self.logger = logging.getLogger('FileEventHandler')
        
        def on_created(self, event):
            if not event.is_directory:
                self.logger.info(f"检测到新文件: {event.src_path}")
                # 将文件路径放入队列（生产者）
                try:
                    # 非阻塞方式放入队列，如果队列满则等待
                    self.file_queue.put(event.src_path, block=True, timeout=5)
                    self.logger.debug(f"文件已加入队列: {event.src_path}")
                except queue.Full:
                    self.logger.warning(f"队列已满，无法添加文件: {event.src_path}")
                except Exception as e:
                    self.logger.error(f"添加文件到队列失败: {event.src_path} - {e}")
    
    def _consumer_worker(self):
        """消费者工作线程"""
        self.logger.info(f"文件消费者线程启动: {threading.current_thread().name}")
        
        while not self.stop_event.is_set():
            try:
                # 从队列获取文件路径，最多等待1秒
                file_path = self.file_queue.get(timeout=1)
                
                # 处理特殊信号（用于停止线程）
                if file_path is None:
                    self.logger.debug("收到停止信号，消费者线程退出")
                    break
                
                # 处理文件
                self._process_file(file_path)
                
                # 标记任务完成
                self.file_queue.task_done()
                
            except queue.Empty:
                # 队列为空，继续等待
                continue
            except Exception as e:
                self.logger.error(f"消费者线程处理文件失败: {e}")
                # 即使处理失败，也标记任务完成，避免队列阻塞
                if not self.file_queue.empty():
                    self.file_queue.task_done()
        
        self.logger.info(f"文件消费者线程退出: {threading.current_thread().name}")
    
    def _process_file(self, file_path: str):
        """处理文件：解析任务数据"""
        try:
            # 等待文件写入完成
            time.sleep(0.5)
            
            if not os.path.exists(file_path):
                self.logger.warning(f"文件不存在: {file_path}")
                return
            
            filename = os.path.basename(file_path)
            self.logger.info(f"开始处理文件: {filename}")
            
            # 解析任务信息
            task_info = self._parse_filename(filename)
            if not task_info:
                self.logger.debug(f"临时文件，跳过解析存储: {filename}")
                return
            
            task_category = task_info['task_category']
            file_ext = task_info['file_ext']    
            
            # 如果文件名的任务类别为"0"，并且后缀为jpg，就不保存文件
            if self._is_origin_data(task_category, file_ext):
                self.logger.debug(f"原始图像，执行语义化数据解析: {filename}")
                return
            
            # 读取文件内容
            with open(file_path, 'rb') as f:
                content = f.read()
                
            self._parse_and_store_data(task_info, content, file_path)
            
        except Exception as e:
            self.logger.error(f"处理文件失败 {file_path}: {e}")
    
    def _parse_filename(self, filename: str) -> Dict[str, str]:
        """
        解析文件名格式：卫星id_任务类型_时间戳.ext
        
        :param filename: 文件名
        :return: 解析后的任务信息字典
        """
        try:
            base_name = os.path.splitext(filename)[0]
            file_ext = os.path.splitext(filename)[1].lower()
            parts = base_name.split('_')
            
            if len(parts) < 3:
                self.logger.debug("文件名解析完成，不满足三部分要求")
                return None
                
            self.logger.debug(f"文件名解析成功，sat_id={parts[0]},task_info={parts[1]},timestamp={parts[2]}")    
            sat_id = parts[0]
            task_category = parts[1]
            try:
                dt = parser.parse(parts[2])
                # 处理无时区的情况（视为UTC）
                if dt.tzinfo is None:
                    dt = dt.replace(tzinfo=datetime.timezone.utc)
                # 转换为Unix时间戳（秒）后转毫秒
                timestamp = int(dt.timestamp() * 1000)
            except Exception as e:
                timestamp = parts[2]
            
            return {
                'filename': filename,
                'sat_id': sat_id, 
                'task_category': str(task_category),
                'timestamp': timestamp,
                'file_ext': file_ext
            }
            
        except Exception as e:
            self.logger.error(f"解析文件名失败: {filename} - {e}")
            return None
    
    def _is_origin_data(self, task_category: str, file_ext: str) -> bool:
        """判断是否为源图像数据"""
        # 任务类别为"0"，文件后缀为.jpg
        return task_category == '0' and file_ext ==  '.jpg'
    
    def _parse_and_store_data(self, task_info: dict, content: bytes, file_path: str):
        """解析并存储任务数据"""
        try:
            if task_info['task_category'] == '0' and task_info['file_ext'] == '.json':
                self._store_origin_data(content, task_info)
            elif task_info['task_category'] == 'BHSF' and task_info['file_ext'] == '.json':
                self._store_feature_data_BHSF(content, task_info)
            elif task_info['task_category'] == 'WZZH' and task_info['file_ext'] == '.json':
                self._store_feature_data_WZZH(content, task_info)
            elif task_info['task_category'] == 'RHDW' and task_info['file_ext'] == '.json':
                self._store_feature_data_RHDW(content, task_info)
            elif task_info['task_category'] == 'XDSF' and task_info['file_ext'] == '.json':
                self._store_feature_data_XDSF(content, task_info)
            elif task_info['task_category'] == 'BKSF' and task_info['file_ext'] == '.json':
                self._store_feature_data_BKSF(content, task_info)
            elif task_info['task_category'] == 'FXJG' and task_info['file_ext'] == '.json':
                self._store_decision_data(content, task_info)
            else:
                self.logger.warning(f"未知的任务类别或文件类型: {task_info['task_category']} {task_info['file_ext']}")
        except Exception as e:
            self.logger.error(f"任务数据存储失败: {file_path} - {e}")
    
    def _store_origin_data(self, content: bytes, task_info: Dict[str, str]):
        """
        存储源数据到origional_table和truth_table表
        
        :param content: 文件内容
        :param task_info: 任务信息
        """
        try:
            filename = task_info['filename']
            # 解析JSON内容
            json_data = json.loads(content.decode('utf-8'))
            
            device_info = json_data.get('deviceInfo', {})
            selected_image = json_data.get('selectedImage', {})
            
            # 根据task.txt中的映射关系进行类型转换
            device_type_map = {0: 'GEO', 131072: 'LEO', 131073: 'LEO_SAR', 131074: 'LEO_RADAR', 131075: 'LEO_ELECTRO'}
            equip_type_map = {1: 'CCD', 2: 'IR', 3: 'SAR', 4: 'RADAR', 5: 'ELECTRO', 6: 'PAN'}
            target_type_map = {
                101: 'AIRPORT', 102: 'PORT', 1201: 'CARRIER', 1202: 'BATTLESHIP', 
                1203: 'DESTROYER', 1204: 'SUPPLY', 1205: 'FRIGATE'
            }
            
            # 提取基本信息用于origional_table
            origin_data = {
                'sat_id': str(task_info.get('sat_id', '')),
                'sat_category': device_type_map.get(device_info.get('deviceType', 0), 'OTHER'),
                'sat_load_category': equip_type_map.get(device_info.get('equipType', 0), 'OTHER'),
                'planed_start_time': str(device_info.get('planedStartTime', 0)),
                'planed_end_time': str(device_info.get('planedEndTime', 0)),
                'timestamp': str(task_info.get('timestamp', 0)),
                
                # 文件相关信息
                'file_size': selected_image.get('imageSize', 0.0),
                'file_name': selected_image.get('imageName', filename),
                'file_path': selected_image.get('imagePath', ''),
                'image_truth_width': selected_image.get('image_truth_width', 0.0),
                'image_resolution': selected_image.get('image_resolution', 0.0),
                
                # 视线中心点ENU坐标系信息
                'enu_base_longitude': selected_image.get('enuBaseLon', 0.0),
                'enu_base_latitude': selected_image.get('enuBaseLat', 0.0),
                'enu_base_altitude': selected_image.get('enuBaseAlt', 0.0),
                
                # 卫星位置信息
                'sat_enu_x': selected_image.get('satPosEnuX', 0.0),
                'sat_enu_y': selected_image.get('satPosEnuY', 0.0),
                'sat_enu_z': selected_image.get('satPosEnuZ', 0.0),
                
                # 相机四元数信息
                'cam_enu_x': selected_image.get('camQENU_X', 0.0),
                'cam_enu_y': selected_image.get('camQENU_Y', 0.0),
                'cam_enu_z': selected_image.get('camQENU_Z', 0.0),
                'cam_enu_w': selected_image.get('camQENU_W', 0.0),
                
                # J2000坐标系信息
                'sat_j2000_x': selected_image.get('satPosJ2000X', 0.0),
                'sat_j2000_y': selected_image.get('satPosJ2000Y', 0.0),
                'sat_j2000_z': selected_image.get('satPosJ2000Z', 0.0),
                
                # 四元数信息
                'sat_qbi_x': selected_image.get('satQbiX', 0.0),
                'sat_qbi_y': selected_image.get('satQbiY', 0.0),
                'sat_qbi_z': selected_image.get('satQbiZ', 0.0),
                'sat_qbi_w': selected_image.get('satQbiW', 0.0),
                
                # 载荷视场角
                'load_fov_x': selected_image.get('fovX', 0.0),
                'load_fov_y': selected_image.get('fovY', 0.0),
                
                # 图像像素信息
                'image_pixel_x': selected_image.get('camPixelX', 0.0),
                'image_pixel_y': selected_image.get('camPixelY', 0.0)
            }
            
            # 插入origional_table数据
            origin_record_id = self.db_manager.insert_origin_data(origin_data)
            if origin_record_id:
                self.logger.info(f"成功插入源数据，记录ID: {origin_record_id}")
            else:
                self.logger.error(f"插入源数据失败: {filename}")
                return
            
            # 处理目标信息，插入truth_table
            target_info_list = selected_image.get('targetInfo', [])
            target_num = selected_image.get('targetNum', 0)
            
            if target_num > 0 and target_info_list:
                for i, target_info in enumerate(target_info_list[:target_num]):
                    # 提取目标信息
                    drawbox = target_info.get('drawbox', {})
                    returnbox = target_info.get('returnbox', {})
                    
                    truth_data = {
                        'sat_id': str(task_info.get('sat_id', '')),
                        'timestamp': str(task_info.get('timestamp', 0)),
                        'target_id': str(target_info.get('targetID', '')),
                        'fleet_number': str(target_info.get('targetFleetNumber', '')),
                        'target_category': target_type_map.get(target_info.get('targetType', 0), 'OTHER'),
                        'target_direction': str(target_info.get('targetDirection', 0.0)),
                        
                        # 目标位置信息
                        'target_longitude': target_info.get('targetPosLon', 0.0),
                        'target_latitude': target_info.get('targetPosLat', 0.0),
                        'target_altitude': target_info.get('targetPosAlt', 0.0),
                        
                        # 卫星坐标系下的目标位置
                        'target_sat_x': target_info.get('targetPosInSatX', 0.0),
                        'target_sat_y': target_info.get('targetPosInSatY', 0.0),
                        'target_sat_z': target_info.get('targetPosInSatZ', 0.0),

                        # 目标像素位置信息
                        'target_x': returnbox.get('x', 0.0),
                        'target_y': returnbox.get('y', 0.0),
                        'target_width': returnbox.get('width', 0.0),
                        'target_height': returnbox.get('height', 0.0)
                    }
                    
                    # 插入truth_table数据
                    truth_record_id = self.db_manager.insert_truth_data(truth_data)
                    if truth_record_id:
                        self.logger.debug(f"成功插入目标数据 {i+1}/{target_num}，记录ID: {truth_record_id}")
                    else:
                        self.logger.error(f"插入目标数据失败: {filename}, 目标 {i+1}")
                
                self.logger.info(f"处理了 {len(target_info_list[:target_num])} 个目标信息")
            else:
                self.logger.info(f"文件 {filename} 中没有目标信息")
                
            self.logger.info(f"源数据已成功存储到数据库: {filename}")
            
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败 {filename}: {e}")
        except Exception as e:
            self.logger.error(f"存储源数据失败 {filename}: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")

    def _store_feature_data_BHSF(self, content: bytes,  task_info: Dict[str, str]):
        """
        存储特征数据到feature_table表
        
        :param content: 文件内容
        :param task_info: 任务信息
        """

        try:
            # 目标类型映射
            target_type_map = {
                101: 'AIRPORT', 102: 'PORT', 1201: 'CARRIER', 1202: 'BATTLESHIP', 
                1203: 'DESTROYER', 1204: 'SUPPLY', 1205: 'FRIGATE'
            }
            filename = task_info['filename']
            json_data = json.loads(content.decode('utf-8'))
            for data_item in json_data:
                target_image_path = data_item.get('image_path','')
                target_id = data_item.get('target_id')
                target_category = data_item.get('type')
                target_truth = data_item.get('truth')
                position = data_item.get('position', {})
                target_x = position.get('x1', 0)
                target_y = position.get('y1', 0)
                target_width = position.get('width', 0)
                target_height = position.get('height', 0)
                
                feature_data = {
                    'sat_id': task_info['sat_id'],
                    'fleet_number': 0,
                    'timestamp': task_info['timestamp'],
                    'target_category': target_type_map.get(target_category, 'OTHER'),
                    'target_id': target_id,
                    'target_image_path': target_image_path,
                    'target_truth': target_truth,
                    'target_x': target_x,
                    'target_y': target_y,
                    'target_width': target_width,
                    'target_height': target_height
                }
                
                record_id = self.db_manager.insert_feature_data(feature_data)
                if record_id:
                    self.logger.info(f"身份识别数据存储成功: target_id={feature_data['target_id']}, "
                                    f"type={feature_data['target_category']}, truth={feature_data['target_truth']} (ID: {record_id})")
            
        except (json.JSONDecodeError, ValueError) as e:
            self.logger.error(f"解析JSON特征数据失败: {filename} - {e}")
            
    def _store_feature_data_WZZH(self, content: bytes, task_info: Dict[str, str]):
        """处理位置转换(WZZH)算法数据
        使用sat_id, timestamp, target_id作为约束条件更新数据库
        """
        try:
            sat_id = task_info['sat_id']
            timestamp = task_info['timestamp']
            json_data = json.loads(content.decode('utf-8'))
            results = json_data.get('results', [])
            
            if not results:
                self.logger.warning(f"位置转换数据为空: sat_id={sat_id}")
                return
            
            self.logger.info(f"处理位置转换数据: sat_id={sat_id}, 目标数量={len(results)}")
            
            success_count = 0
            failed_count = 0
            
            for result in results:
                target_id = str(result.get('target_id', ''))
                lat = float(result.get('Latitude', 0.0))  # 纬度
                lon = float(result.get('longitude', 0.0))  # 经度
                
                # 使用统一的update_WZZH方法
                if self.db_manager.update_WZZH(sat_id, timestamp, target_id, lon, lat):
                    success_count += 1
                else:
                    failed_count += 1
                    
            self.logger.info(f"位置转换数据处理完成: "
                           f"成功{success_count}条记录, 失败{failed_count}条记录")
                    
        except Exception as e:
            self.logger.error(f"处理位置转换数据失败: {e}")
                    
    def _store_feature_data_RHDW(self, content: bytes, task_info: Dict[str, str]):
        """处理融合定位(RHDW)算法数据
        使用sat_id, timestamp, target_id作为约束条件更新/插入数据库
        """
        try:
            sat_id = task_info['sat_id']
            timestamp = task_info['timestamp']
            json_data = json.loads(content.decode('utf-8'))
            fused_positions = json_data.get('fused_positions', [])
            
            if not fused_positions:
                self.logger.warning(f"融合定位数据为空: sat_id={sat_id}")
                return
            
            self.logger.info(f"处理融合定位数据: sat_id={sat_id}, 目标数量={len(fused_positions)}")
            
            success_count = 0
            failed_count = 0
            
            # 目标类型映射
            target_type_map = {
                101: 'AIRPORT', 102: 'PORT', 1201: 'CARRIER', 1202: 'BATTLESHIP', 
                1203: 'DESTROYER', 1204: 'SUPPLY', 1205: 'FRIGATE'
            }
            
            for position in fused_positions:
                target_id = str(position.get('target_id', ''))
                target_type = position.get('target_type', 0)
                fused_lon = float(position.get('fused_lon', 0.0))
                fused_lat = float(position.get('fused_lat', 0.0))
                lon_velocity = float(position.get('lon_velocity', 0.0))
                lat_velocity = float(position.get('lat_velocity', 0.0))
                speed = float(position.get('speed', 0.0))
                
                # 检查约束条件
                if not all([sat_id, timestamp, target_id]):
                    self.logger.warning(f"约束条件不完整: sat_id={sat_id}, timestamp={timestamp}, target_id={target_id}")
                    failed_count += 1
                    continue
                
                # 使用统一的update_RHDW方法
                target_category = target_type_map.get(target_type, 'OTHER')
                if self.db_manager.update_RHDW(sat_id, timestamp, target_id, fused_lon, fused_lat, 
                                             lon_velocity, lat_velocity, speed, target_category):
                    success_count += 1
                else:
                    failed_count += 1
                
            self.logger.info(f"融合定位数据处理完成: "
                           f"成功{success_count}条记录, 失败{failed_count}条记录")
                    
        except Exception as e:
            self.logger.error(f"处理融合定位数据失败: {e}")

    def _store_feature_data_XDSF(self, content: bytes,  task_info: Dict[str, str]):
        """
        存储特征数据到feature_table表
        
        :param content: 文件内容
        :param task_info: 任务信息
        """

        try:
            # 目标类型映射
            target_type_map = {
                101: 'AIRPORT', 102: 'PORT', 1201: 'CARRIER', 1202: 'BATTLESHIP', 
                1203: 'DESTROYER', 1204: 'SUPPLY', 1205: 'FRIGATE'
            }
            filename = task_info['filename']
            json_data = json.loads(content.decode('utf-8'))
            for data_item in json_data:
                target_image_path = data_item.get('image_path','')
                target_id = data_item.get('target_id')
                target_category = data_item.get('type')
                target_npy_path = data_item.get('npy_path')
                
                feature_data = {
                    'sat_id': task_info['sat_id'],
                    'fleet_number': 0,
                    'timestamp': task_info['timestamp'],
                    'target_category': target_type_map.get(target_category, 'OTHER'),
                    'target_id': target_id,
                    'target_image_path': target_image_path,
                    'target_npy_path': target_npy_path,
                }
                
                record_id = self.db_manager.insert_feature_data(feature_data)
                if record_id:
                    self.logger.info(f"身份识别数据存储成功: target_id={feature_data['target_id']}, "
                                    f"type={feature_data['target_category']}, truth={feature_data['target_truth']} (ID: {record_id})")
            
        except (json.JSONDecodeError, ValueError) as e:
            self.logger.error(f"解析JSON特征数据失败: {filename} - {e}")
     
    def _store_feature_data_BKSF(self, content: bytes,  task_info: Dict[str, str]):
        """
        存储特征数据到feature_table表
        
        :param content: 文件内容
        :param task_info: 任务信息
        """

        try:
            # 目标类型映射
            target_type_map = {
                101: 'AIRPORT', 102: 'PORT', 1201: 'CARRIER', 1202: 'BATTLESHIP', 
                1203: 'DESTROYER', 1204: 'SUPPLY', 1205: 'FRIGATE'
            }
            filename = task_info['filename']
            json_data = json.loads(content.decode('utf-8'))
            targets = json_data.get('targets', [])
            for data_item in targets:
                target_id = data_item.get('TargetID')
                target_latitude = data_item.get('Latitude')
                target_longitude = data_item.get('Longitude')
                target_altitude = data_item.get('Height')
                feature_data = {
                    'sat_id': task_info['sat_id'],
                    'timestamp': task_info['timestamp'],
                    'target_id': target_id,
                    'target_category': target_type_map.get(target_id, 'OTHER'),
                    'target_latitude': target_latitude,
                    'target_longitude': target_longitude,
                    'target_altitude': target_altitude,
                }
                
                record_id = self.db_manager.insert_feature_data(feature_data)
                if record_id:
                    self.logger.info(f"身份识别数据存储成功: target_id={feature_data['target_id']}, "
                                    f"latitude={feature_data['target_latitude']}, longitude={feature_data['target_longitude']}, altitude={feature_data['target_altitude']} (ID: {record_id})")
            
        except (json.JSONDecodeError, ValueError) as e:
            self.logger.error(f"解析JSON特征数据失败: {filename} - {e}")
     
    def _decision_tool(self, query_days: str, fleet_number: str, info: Dict[str, Any], timestamp: str) -> bool:
        """
        决策工具
        
        :param info: 舰队信息
        """
        if info.get('success'):
            intention = info.get('intention', '')
            situation = info.get('situation', {})
            lat = situation.get('lat', 0)
            lon = situation.get('lon', 0)
            speed = situation.get('speed_kmh', 0)
            heading_angle = situation.get('heading_angle', 0)
            threat = situation.get('threat', {})
            speed_threat = threat.get('speed_threat', 0)
            heading_threat = threat.get('heading_threat', 0)
            distance_threat = threat.get('distance_threat', 0)
            # 准备基础决策数据
            decision_data = {
                'timestamp': timestamp,
                'query_days': query_days,
                'fleet_number': fleet_number,
                'speed_threat': speed_threat,
                'heading_threat': heading_threat,
                'distance_threat': distance_threat,
                'heading_angle': heading_angle,
                'speed': speed,
                'lat': lat,
                'lon': lon,
                'intention': intention,
            }
            
            # 插入数据库
            record_id = self.db_manager.insert_decision_data(decision_data)
            if record_id:
                self.logger.info(f"决策数据存储成功:decision_table (ID: {record_id})")
                self.logger.debug(f"决策数据详情: {decision_data}")
                return True
            else:
                self.logger.error(f"决策数据存储失败")
                return False
        else:
            error = info.get("error")
            self.logger.info(f"{fleet_number}执行情况：{error}")
            return False
    
    def _store_decision_data(self, content: bytes, task_info: Dict[str, str]):
        """
        存储决策数据到decision_table表
        
        :param content: 文件内容
        :param task_info: 任务信息
        """

        try:
            json_data = json.loads(content.decode('utf-8'))
            
            query_days = json_data.get('query_days', '')
            FLEET_01 = json_data.get('FLEET_01', {})
            self._decision_tool(query_days, 'FLEET_01', FLEET_01, task_info['timestamp'])

            FLEET_02 = json_data.get('FLEET_02', {})
            self._decision_tool(query_days, 'FLEET_02', FLEET_02, task_info['timestamp'])

            FLEET_03 = json_data.get('FLEET_03', {})
            self._decision_tool(query_days, 'FLEET_03', FLEET_03, task_info['timestamp'])
            
        except (json.JSONDecodeError, ValueError) as e:
            self.logger.error(f"解析JSON决策数据失败: {task_info['filename']} - {e}")

    def start_watching(self):
        """开始监控文件"""
        try:
            # 启动消费者线程
            for i in range(self.num_consumers):
                thread = threading.Thread(
                    target=self._consumer_worker,
                    name=f"FileConsumer-{i+1}",
                    daemon=True
                )
                thread.start()
                self.consumer_threads.append(thread)
            
            self.logger.info(f"启动了 {self.num_consumers} 个文件消费者线程")
            
            # 初始化文件观察者
            self.observer = Observer()
            event_handler = self.FileEventHandler(self.file_queue)
            
            # 为每个目录分别注册监听
            for path in self.watch_directories:
                self.observer.schedule(
                    event_handler,
                    path,
                    recursive=True
                )
            
            self.observer.start()
            self.logger.info(f"开始监控目录: {', '.join(self.watch_directories)}")
            
        except Exception as e:
            self.logger.error(f"启动文件监控失败: {e}")
            raise
    
    def stop_watching(self):
        """停止监控文件"""
        # 设置停止事件
        self.stop_event.set()
        
        # 停止文件观察者
        if self.observer:
            self.observer.stop()
            self.observer.join()
            self.logger.info("文件监控已停止")
        
        # 向队列发送停止信号给所有消费者线程
        for _ in range(len(self.consumer_threads)):
            try:
                self.file_queue.put(None, timeout=5)
            except queue.Full:
                self.logger.warning("队列已满，无法发送停止信号")
        
        # 等待所有消费者线程完成
        for thread in self.consumer_threads:
            thread.join(timeout=10)
            if thread.is_alive():
                self.logger.warning(f"消费者线程 {thread.name} 未能正常退出")
        
        self.logger.info("文件处理消费者线程已关闭")
        
        # 等待队列中所有任务完成
        try:
            self.file_queue.join()
            self.logger.info("文件队列中的所有任务已完成")
        except Exception as e:
            self.logger.error(f"等待队列任务完成时出错: {e}")

class DatabaseService:
    """数据库服务主类"""
    
    def __init__(self, config_path: str = "config.ini"):
        self.config_path = config_path
        self.config = self._load_config(config_path)
        
        # 设置日志
        self._setup_logging()
        
        self.logger = logging.getLogger('DatabaseService')
        self.db_manager = None
        self.file_watcher = None
        self.running = False
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        config = configparser.ConfigParser()
        config.read(config_path, encoding='utf-8')
        
        result = {}
        for section in config.sections():
            result[section] = dict(config[section])
        
        return result
    
    def _setup_logging(self):
        """设置日志配置"""
        log_config = self.config.get('logging', {})
        
        logging.basicConfig(
            level=getattr(logging, log_config.get('level', 'INFO')),
            format=log_config.get('format', '[%(asctime)s] [%(name)s] %(message)s'),
            datefmt=log_config.get('date_format', '%Y-%m-%d %H:%M:%S')
        )
    
    def initialize(self):
        """初始化服务"""
        try:
            self.logger.info("初始化数据库服务...")
            
            # 初始化数据库管理器
            self.db_manager = DatabaseManager(self.config_path)
            
            # 初始化文件监控器
            if self.config['database'].get('database_enable', 'true').lower() == 'true':
                self.file_watcher = FileWatcher(self.db_manager, self.config)
                self.logger.info("数据库服务初始化完成")
            else:
                self.logger.info("数据库服务已禁用")
            
        except Exception as e:
            self.logger.error(f"初始化数据库服务失败: {e}")
            raise
    
    def start(self):
        """启动服务"""
        try:
            if not self.config['database'].get('database_enable', 'true').lower() == 'true':
                self.logger.info("数据库服务已禁用，跳过启动")
                return
            
            self.running = True
            self.logger.info("启动数据库服务...")
            
            # 启动文件监控
            if self.file_watcher:
                self.file_watcher.start_watching()
            
            self.logger.info("数据库服务启动完成")
            
        except Exception as e:
            self.logger.error(f"启动数据库服务失败: {e}")
            raise
    
    def stop(self):
        """停止服务"""
        try:
            self.running = False
            self.logger.info("停止数据库服务...")
            
            # 停止文件监控
            if self.file_watcher:
                self.file_watcher.stop_watching()
            
            # 关闭数据库连接
            if self.db_manager:
                self.db_manager.close()
            
            self.logger.info("数据库服务已停止")
            
        except Exception as e:
            self.logger.error(f"停止数据库服务失败: {e}")

def main():
    """主函数 - 独立运行数据库服务"""
    import signal
    import sys
    
    service = DatabaseService()
    
    def signal_handler(signum, frame):
        print(f"\n收到信号 {signum}，正在关闭数据库服务...")
        service.stop()
        sys.exit(0)
    
    # 设置信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 初始化并启动服务
        service.initialize()
        service.start()
        
        print("数据库服务正在运行，按 Ctrl+C 退出...")
        
        # 保持运行
        while service.running:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n收到中断信号，正在关闭服务...")
    except Exception as e:
        print(f"数据库服务运行失败: {e}")
        sys.exit(1)
    finally:
        service.stop()


if __name__ == "__main__":
    main()