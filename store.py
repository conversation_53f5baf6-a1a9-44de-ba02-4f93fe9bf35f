#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分布式数据收集和存储模块 - 基于ZeroMQ、SQLite和Watchdog的三线程系统
实现两个生产者线程和一个消费者线程的分布式数据收集和存储功能

创建时间: 2025-01-25
"""

import configparser
import json
import logging
import os
import sqlite3
import time
import queue
import threading
import zmq
import re
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from contextlib import contextmanager
from queue import Queue, Empty
from threading import Lock, Event
from typing import Dict, Any, Optional, List, Tuple
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler


class StoreConnectionPool:
    """SQLite连接池管理器 - 专用于store模块"""
    
    def __init__(self, db_path: str, max_connections: int = 10):
        self.db_path = db_path
        self.max_connections = max_connections
        self.connections = Queue(maxsize=max_connections)
        self.lock = Lock()
        self.logger = logging.getLogger('StoreConnectionPool')
        self._initialize_pool()
        
    def _initialize_pool(self):
        """初始化连接池"""
        for _ in range(self.max_connections):
            conn = self._create_connection()
            if conn:
                self.connections.put(conn)
        self.logger.info(f"数据库连接池初始化完成，连接数: {self.max_connections}")
    
    def _create_connection(self) -> Optional[sqlite3.Connection]:
        """创建新的数据库连接"""
        try:
            conn = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=30.0
            )
            
            # 启用WAL模式和优化设置
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=-64000")
            conn.execute("PRAGMA temp_store=MEMORY")
            conn.execute("PRAGMA mmap_size=268435456")
            conn.execute("PRAGMA foreign_keys=ON")
            conn.execute("PRAGMA auto_vacuum=INCREMENTAL")
            conn.execute("PRAGMA busy_timeout=30000")
            
            conn.row_factory = sqlite3.Row
            return conn
            
        except Exception as e:
            self.logger.error(f"创建数据库连接失败: {e}")
            return None
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = None
        try:
            with self.lock:
                if not self.connections.empty():
                    conn = self.connections.get_nowait()
                else:
                    conn = self._create_connection()
                    
            if conn is None:
                raise Exception("无法获取数据库连接")
                
            yield conn
            
        except Exception as e:
            self.logger.error(f"数据库操作错误: {e}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                try:
                    conn.commit()
                    with self.lock:
                        if not self.connections.full():
                            self.connections.put(conn)
                        else:
                            conn.close()
                except Exception as e:
                    self.logger.error(f"归还连接时出错: {e}")
                    conn.close()
    
    def close_all(self):
        """关闭所有连接"""
        with self.lock:
            while not self.connections.empty():
                try:
                    conn = self.connections.get_nowait()
                    conn.close()
                except Exception as e:
                    self.logger.error(f"关闭连接时出错: {e}")


class FileStabilityChecker:
    """文件稳定性检测器 - 检测文件大小50ms内未变化"""
    
    def __init__(self, stability_time: float = 0.05):
        self.stability_time = stability_time  # 50ms
        self.logger = logging.getLogger('FileStabilityChecker')
    
    def is_file_stable(self, file_path: str) -> bool:
        """检查文件是否稳定（大小50ms内未变化）"""
        try:
            if not os.path.exists(file_path):
                return False
            
            # 获取初始文件大小
            initial_size = os.path.getsize(file_path)
            initial_mtime = os.path.getmtime(file_path)
            
            # 等待稳定时间
            time.sleep(self.stability_time)
            
            # 检查文件大小是否变化
            if not os.path.exists(file_path):
                return False
                
            current_size = os.path.getsize(file_path)
            current_mtime = os.path.getmtime(file_path)
            
            # 文件大小和修改时间都未变化则认为稳定
            is_stable = (initial_size == current_size and initial_mtime == current_mtime)
            
            if is_stable:
                self.logger.debug(f"文件稳定: {file_path}")
            else:
                self.logger.debug(f"文件不稳定: {file_path}")
                
            return is_stable
            
        except Exception as e:
            self.logger.error(f"检查文件稳定性失败 {file_path}: {e}")
            return False


class FilenameParser:
    """文件名解析器 - 解析 {id}_{type}_{timestamp}.json 格式"""
    
    def __init__(self):
        # 匹配 {id}_{type}_{timestamp}.json 格式
        self.pattern = re.compile(r'^(.+)_(\d+)_(.+)\.json$')
        self.logger = logging.getLogger('FilenameParser')
    
    def parse_filename(self, filename: str) -> Optional[Dict[str, str]]:
        """解析文件名，返回 {id, type, timestamp} 或 None"""
        try:
            match = self.pattern.match(os.path.basename(filename))
            if match:
                file_id, file_type, timestamp = match.groups()
                
                # 确定数据类型
                data_type = "original_data" if file_type == "0" else "feature_data"
                
                return {
                    'id': file_id,
                    'type': file_type,
                    'data_type': data_type,
                    'timestamp': timestamp,
                    'filename': filename
                }
            else:
                self.logger.debug(f"文件名格式不匹配: {filename}")
                return None
                
        except Exception as e:
            self.logger.error(f"解析文件名失败 {filename}: {e}")
            return None


class DirectoryMonitorThread:
    """生产者线程1 - 目录监控器"""
    
    def __init__(self, config: Dict[str, Any], message_queue: Queue, zmq_context: zmq.Context):
        self.config = config
        self.message_queue = message_queue
        self.zmq_context = zmq_context
        self.publisher = None
        self.observer = None
        self.running = False
        self.stop_event = Event()
        
        # 配置参数
        self.watch_directories = config.get('watch_directories', [])
        self.zmq_publish_addr = config.get('zmq_publish_addr', 'tcp://0.0.0.0:13232')
        
        # 工具类
        self.stability_checker = FileStabilityChecker()
        self.filename_parser = FilenameParser()
        
        self.logger = logging.getLogger('DirectoryMonitor')
    
    def _setup_zmq_publisher(self):
        """设置ZeroMQ发布器"""
        try:
            self.publisher = self.zmq_context.socket(zmq.PUB)
            self.publisher.setsockopt(zmq.SNDHWM, 10000)
            self.publisher.setsockopt(zmq.LINGER, 1000)
            self.publisher.setsockopt(zmq.SNDBUF, 1024 * 1024)
            self.publisher.bind(self.zmq_publish_addr)
            self.logger.info(f"ZeroMQ发布器绑定到: {self.zmq_publish_addr}")
            return True
        except Exception as e:
            self.logger.error(f"设置ZeroMQ发布器失败: {e}")
            return False
    
    class FileEventHandler(FileSystemEventHandler):
        """文件系统事件处理器"""
        
        def __init__(self, monitor_thread):
            self.monitor = monitor_thread
            self.logger = logging.getLogger('FileEventHandler')
        
        def on_created(self, event):
            if not event.is_directory and event.src_path.endswith('.json'):
                self.monitor._handle_file_event(event.src_path)
        
        def on_modified(self, event):
            if not event.is_directory and event.src_path.endswith('.json'):
                self.monitor._handle_file_event(event.src_path)
    
    def _handle_file_event(self, file_path: str):
        """处理文件事件"""
        try:
            # 解析文件名
            parsed = self.filename_parser.parse_filename(file_path)
            if not parsed:
                return
            
            # 等待文件稳定
            if not self.stability_checker.is_file_stable(file_path):
                self.logger.debug(f"文件不稳定，跳过: {file_path}")
                return
            
            # 读取文件内容
            with open(file_path, 'rb') as f:
                file_content = f.read()
            
            # 发布到ZeroMQ
            topic = parsed['data_type']
            message = [topic.encode(), os.path.basename(file_path).encode(), file_content]
            
            if self.publisher:
                self.publisher.send_multipart(message)
                self.logger.info(f"发布文件到ZeroMQ: {file_path} (topic: {topic})")
            
            # 放入内部消息队列
            queue_message = (topic, os.path.basename(file_path), file_content)
            self.message_queue.put(queue_message)
            self.logger.debug(f"文件放入内部队列: {file_path}")
            
        except Exception as e:
            self.logger.error(f"处理文件事件失败 {file_path}: {e}")
    
    def start(self):
        """启动目录监控线程"""
        try:
            self.running = True
            
            # 设置ZeroMQ发布器
            if not self._setup_zmq_publisher():
                raise Exception("ZeroMQ发布器设置失败")
            
            # 设置文件监控
            self.observer = Observer()
            event_handler = self.FileEventHandler(self)
            
            for directory in self.watch_directories:
                if os.path.exists(directory):
                    self.observer.schedule(event_handler, directory, recursive=True)
                    self.logger.info(f"开始监控目录: {directory}")
                else:
                    self.logger.warning(f"监控目录不存在: {directory}")
            
            self.observer.start()
            self.logger.info("目录监控线程启动成功")
            
        except Exception as e:
            self.logger.error(f"启动目录监控线程失败: {e}")
            raise
    
    def stop(self):
        """停止目录监控线程"""
        self.running = False
        self.stop_event.set()
        
        if self.observer:
            self.observer.stop()
            self.observer.join()
        
        if self.publisher:
            self.publisher.close()
        
        self.logger.info("目录监控线程已停止")


class NetworkSubscriberThread:
    """生产者线程2 - 网络订阅器"""

    def __init__(self, config: Dict[str, Any], message_queue: Queue, zmq_context: zmq.Context):
        self.config = config
        self.message_queue = message_queue
        self.zmq_context = zmq_context
        self.subscribers = []
        self.running = False
        self.stop_event = Event()
        self.worker_threads = []

        # 配置参数
        self.cluster_nodes = config.get('cluster_nodes', [])
        self.subscribe_topics = config.get('subscribe_topics', ['original_data', 'feature_data'])

        self.logger = logging.getLogger('NetworkSubscriber')

    def _create_subscriber(self, endpoint: str) -> zmq.Socket:
        """创建ZeroMQ订阅器"""
        try:
            subscriber = self.zmq_context.socket(zmq.SUB)
            subscriber.setsockopt(zmq.RCVHWM, 10000)
            subscriber.setsockopt(zmq.LINGER, 1000)
            subscriber.setsockopt(zmq.RCVBUF, 1024 * 1024)

            # 订阅指定主题
            for topic in self.subscribe_topics:
                subscriber.setsockopt_string(zmq.SUBSCRIBE, topic)
                self.logger.debug(f"订阅主题: {topic} 从 {endpoint}")

            subscriber.connect(endpoint)
            self.logger.info(f"连接到ZeroMQ端点: {endpoint}")
            return subscriber

        except Exception as e:
            self.logger.error(f"创建订阅器失败 {endpoint}: {e}")
            return None

    def _subscriber_worker(self, endpoint: str):
        """订阅器工作线程"""
        subscriber = self._create_subscriber(endpoint)
        if not subscriber:
            return

        self.logger.info(f"订阅器工作线程启动: {endpoint}")

        try:
            while self.running and not self.stop_event.is_set():
                try:
                    # 非阻塞接收消息
                    if subscriber.poll(1000):  # 1秒超时
                        message_parts = subscriber.recv_multipart(zmq.NOBLOCK)

                        if len(message_parts) >= 3:
                            topic = message_parts[0].decode()
                            filename = message_parts[1].decode()
                            file_content = message_parts[2]

                            # 根据主题处理消息
                            self._handle_received_message(topic, filename, file_content, endpoint)

                except zmq.Again:
                    # 没有消息可接收，继续循环
                    continue
                except Exception as e:
                    self.logger.error(f"接收消息失败 {endpoint}: {e}")
                    time.sleep(1)

        except Exception as e:
            self.logger.error(f"订阅器工作线程异常 {endpoint}: {e}")
        finally:
            if subscriber:
                subscriber.close()
            self.logger.info(f"订阅器工作线程退出: {endpoint}")

    def _handle_received_message(self, topic: str, filename: str, file_content: bytes, endpoint: str):
        """处理接收到的消息"""
        try:
            # 根据主题进行不同处理
            if topic == 'original_data':
                self.logger.debug(f"接收到原始数据: {filename} 从 {endpoint}")
            elif topic == 'feature_data':
                self.logger.debug(f"接收到特征数据: {filename} 从 {endpoint}")
            else:
                self.logger.warning(f"未知主题: {topic} 从 {endpoint}")
                return

            # 放入内部消息队列
            queue_message = (filename, file_content)
            self.message_queue.put(queue_message)
            self.logger.debug(f"网络消息放入内部队列: {filename}")

        except Exception as e:
            self.logger.error(f"处理接收消息失败 {filename}: {e}")

    def start(self):
        """启动网络订阅线程"""
        try:
            self.running = True

            # 为每个集群节点创建订阅器工作线程
            for node_ip in self.cluster_nodes:
                endpoint = f"tcp://{node_ip}:13232"
                worker_thread = threading.Thread(
                    target=self._subscriber_worker,
                    args=(endpoint,),
                    name=f"Subscriber-{node_ip}",
                    daemon=True
                )
                worker_thread.start()
                self.worker_threads.append(worker_thread)

            self.logger.info(f"网络订阅线程启动成功，订阅 {len(self.cluster_nodes)} 个节点")

        except Exception as e:
            self.logger.error(f"启动网络订阅线程失败: {e}")
            raise

    def stop(self):
        """停止网络订阅线程"""
        self.running = False
        self.stop_event.set()

        # 等待所有工作线程结束
        for thread in self.worker_threads:
            thread.join(timeout=5)
            if thread.is_alive():
                self.logger.warning(f"订阅器线程 {thread.name} 未能正常退出")

        self.logger.info("网络订阅线程已停止")


class DataProcessorThread:
    """消费者线程 - 数据处理器"""

    def __init__(self, config: Dict[str, Any], message_queue: Queue, connection_pool: StoreConnectionPool):
        self.config = config
        self.message_queue = message_queue
        self.connection_pool = connection_pool
        self.running = False
        self.stop_event = Event()

        # 线程池配置
        self.max_workers = config.get('max_workers', 4)
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)

        # 工具类
        self.filename_parser = FilenameParser()

        self.logger = logging.getLogger('DataProcessor')

    def _setup_database_tables(self):
        """设置数据库表结构"""
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()

                # 创建原始数据表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS original_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        file_id TEXT NOT NULL,
                        sat_id TEXT,
                        sat_category TEXT,
                        sat_load_category TEXT,
                        planed_start_time TEXT,
                        planed_end_time TEXT,
                        file_size REAL,
                        file_name TEXT,
                        file_path TEXT,
                        image_truth_width REAL,
                        image_resolution REAL,
                        timestamp TEXT NOT NULL,
                        enu_base_longitude REAL,
                        enu_base_latitude REAL,
                        enu_base_altitude REAL,
                        sat_enu_x REAL,
                        sat_enu_y REAL,
                        sat_enu_z REAL,
                        cam_enu_x REAL,
                        cam_enu_y REAL,
                        cam_enu_z REAL,
                        cam_enu_w REAL,
                        sat_j2000_x REAL,
                        sat_j2000_y REAL,
                        sat_j2000_z REAL,
                        sat_qbi_x REAL,
                        sat_qbi_y REAL,
                        sat_qbi_z REAL,
                        sat_qbi_w REAL,
                        load_fov_x REAL,
                        load_fov_y REAL,
                        image_pixel_x REAL,
                        image_pixel_y REAL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # 创建特征数据表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS feature_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        file_id TEXT NOT NULL,
                        sat_id TEXT NOT NULL,
                        fleet_number TEXT,
                        target_category TEXT,
                        target_id TEXT,
                        timestamp TEXT NOT NULL,
                        target_image_path TEXT,
                        target_truth BOOLEAN,
                        target_x REAL,
                        target_y REAL,
                        target_width REAL,
                        target_height REAL,
                        target_longitude REAL,
                        target_latitude REAL,
                        target_fusion_longitude REAL,
                        target_fusion_latitude REAL,
                        target_longitude_speed REAL,
                        target_latitude_speed REAL,
                        target_total_speed REAL,
                        target_npy_path TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_original_file_id ON original_data(file_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_original_timestamp ON original_data(timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_feature_file_id ON feature_data(file_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_feature_timestamp ON feature_data(timestamp)')

                self.logger.info("数据库表结构设置完成")

        except Exception as e:
            self.logger.error(f"设置数据库表结构失败: {e}")
            raise

    def _process_message(self, message: Tuple):
        """处理单个消息"""
        try:
            if len(message) == 3:
                # 来自目录监控的消息格式: (topic, filename, file_content_bytes)
                topic, filename, file_content = message
                parsed = self.filename_parser.parse_filename(filename)
            elif len(message) == 2:
                # 来自网络订阅的消息格式: (filename, file_content_bytes)
                filename, file_content = message
                parsed = self.filename_parser.parse_filename(filename)
                topic = parsed['data_type'] if parsed else None
            else:
                self.logger.error(f"无效的消息格式: {message}")
                return

            if not parsed:
                self.logger.warning(f"无法解析文件名: {filename}")
                return

            # 解析JSON内容
            try:
                json_data = json.loads(file_content.decode('utf-8'))
            except Exception as e:
                self.logger.error(f"解析JSON失败 {filename}: {e}")
                return

            # 根据数据类型存储到数据库
            if parsed['data_type'] == 'original_data':
                self._store_original_data(parsed, json_data)
            elif parsed['data_type'] == 'feature_data':
                self._store_feature_data(parsed, json_data)
            else:
                self.logger.warning(f"未知数据类型: {parsed['data_type']}")

        except Exception as e:
            self.logger.error(f"处理消息失败: {e}")

    def _store_original_data(self, parsed: Dict[str, str], json_data: Dict[str, Any]):
        """存储原始数据到数据库"""
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()

                # 准备插入数据
                insert_data = {
                    'file_id': parsed['id'],
                    'sat_id': json_data.get('sat_id'),
                    'sat_category': json_data.get('sat_category'),
                    'sat_load_category': json_data.get('sat_load_category'),
                    'planed_start_time': json_data.get('planed_start_time'),
                    'planed_end_time': json_data.get('planed_end_time'),
                    'file_size': json_data.get('file_size'),
                    'file_name': json_data.get('file_name'),
                    'file_path': json_data.get('file_path'),
                    'image_truth_width': json_data.get('image_truth_width'),
                    'image_resolution': json_data.get('image_resolution'),
                    'timestamp': parsed['timestamp'],
                    'enu_base_longitude': json_data.get('enu_base_longitude'),
                    'enu_base_latitude': json_data.get('enu_base_latitude'),
                    'enu_base_altitude': json_data.get('enu_base_altitude'),
                    'sat_enu_x': json_data.get('sat_enu_x'),
                    'sat_enu_y': json_data.get('sat_enu_y'),
                    'sat_enu_z': json_data.get('sat_enu_z'),
                    'cam_enu_x': json_data.get('cam_enu_x'),
                    'cam_enu_y': json_data.get('cam_enu_y'),
                    'cam_enu_z': json_data.get('cam_enu_z'),
                    'cam_enu_w': json_data.get('cam_enu_w'),
                    'sat_j2000_x': json_data.get('sat_j2000_x'),
                    'sat_j2000_y': json_data.get('sat_j2000_y'),
                    'sat_j2000_z': json_data.get('sat_j2000_z'),
                    'sat_qbi_x': json_data.get('sat_qbi_x'),
                    'sat_qbi_y': json_data.get('sat_qbi_y'),
                    'sat_qbi_z': json_data.get('sat_qbi_z'),
                    'sat_qbi_w': json_data.get('sat_qbi_w'),
                    'load_fov_x': json_data.get('load_fov_x'),
                    'load_fov_y': json_data.get('load_fov_y'),
                    'image_pixel_x': json_data.get('image_pixel_x'),
                    'image_pixel_y': json_data.get('image_pixel_y')
                }

                # 构建SQL语句
                columns = ', '.join(insert_data.keys())
                placeholders = ', '.join([f':{key}' for key in insert_data.keys()])
                sql = f"INSERT INTO original_data ({columns}) VALUES ({placeholders})"

                cursor.execute(sql, insert_data)
                self.logger.debug(f"原始数据已存储: {parsed['id']}")

        except Exception as e:
            self.logger.error(f"存储原始数据失败 {parsed['id']}: {e}")

    def _store_feature_data(self, parsed: Dict[str, str], json_data: Dict[str, Any]):
        """存储特征数据到数据库"""
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()

                # 准备插入数据
                insert_data = {
                    'file_id': parsed['id'],
                    'sat_id': json_data.get('sat_id'),
                    'fleet_number': json_data.get('fleet_number'),
                    'target_category': json_data.get('target_category'),
                    'target_id': json_data.get('target_id'),
                    'timestamp': parsed['timestamp'],
                    'target_image_path': json_data.get('target_image_path'),
                    'target_truth': json_data.get('target_truth'),
                    'target_x': json_data.get('target_x'),
                    'target_y': json_data.get('target_y'),
                    'target_width': json_data.get('target_width'),
                    'target_height': json_data.get('target_height'),
                    'target_longitude': json_data.get('target_longitude'),
                    'target_latitude': json_data.get('target_latitude'),
                    'target_fusion_longitude': json_data.get('target_fusion_longitude'),
                    'target_fusion_latitude': json_data.get('target_fusion_latitude'),
                    'target_longitude_speed': json_data.get('target_longitude_speed'),
                    'target_latitude_speed': json_data.get('target_latitude_speed'),
                    'target_total_speed': json_data.get('target_total_speed'),
                    'target_npy_path': json_data.get('target_npy_path')
                }

                # 构建SQL语句
                columns = ', '.join(insert_data.keys())
                placeholders = ', '.join([f':{key}' for key in insert_data.keys()])
                sql = f"INSERT INTO feature_data ({columns}) VALUES ({placeholders})"

                cursor.execute(sql, insert_data)
                self.logger.debug(f"特征数据已存储: {parsed['id']}")

        except Exception as e:
            self.logger.error(f"存储特征数据失败 {parsed['id']}: {e}")

    def _consumer_worker(self):
        """消费者工作线程"""
        self.logger.info(f"数据处理工作线程启动: {threading.current_thread().name}")

        while self.running and not self.stop_event.is_set():
            try:
                # 从队列获取消息，最多等待1秒
                message = self.message_queue.get(timeout=1)

                # 处理特殊停止信号
                if message is None:
                    self.logger.debug("收到停止信号，消费者线程退出")
                    break

                # 提交到线程池处理
                future = self.executor.submit(self._process_message, message)

                # 标记任务完成
                self.message_queue.task_done()

            except Empty:
                # 队列为空，继续等待
                continue
            except Exception as e:
                self.logger.error(f"消费者工作线程异常: {e}")
                # 即使处理失败，也标记任务完成，避免队列阻塞
                if not self.message_queue.empty():
                    self.message_queue.task_done()

        self.logger.info(f"数据处理工作线程退出: {threading.current_thread().name}")

    def start(self):
        """启动数据处理线程"""
        try:
            self.running = True

            # 设置数据库表结构
            self._setup_database_tables()

            # 启动消费者工作线程
            self.worker_thread = threading.Thread(
                target=self._consumer_worker,
                name="DataProcessor-Worker",
                daemon=True
            )
            self.worker_thread.start()

            self.logger.info(f"数据处理线程启动成功，线程池大小: {self.max_workers}")

        except Exception as e:
            self.logger.error(f"启动数据处理线程失败: {e}")
            raise

    def stop(self):
        """停止数据处理线程"""
        self.running = False
        self.stop_event.set()

        # 发送停止信号
        try:
            self.message_queue.put(None, timeout=5)
        except:
            pass

        # 等待工作线程结束
        if hasattr(self, 'worker_thread'):
            self.worker_thread.join(timeout=10)
            if self.worker_thread.is_alive():
                self.logger.warning("数据处理工作线程未能正常退出")

        # 关闭线程池
        self.executor.shutdown(wait=True)

        self.logger.info("数据处理线程已停止")

class StoreService:
    """分布式数据收集和存储服务主类"""

    def __init__(self, config_path: str = "config.ini"):
        self.config_path = config_path
        self.config = {}
        self.running = False
        self.stop_event = Event()

        # 核心组件
        self.zmq_context = None
        self.connection_pool = None
        self.message_queue = None

        # 三个线程
        self.directory_monitor = None
        self.network_subscriber = None
        self.data_processor = None

        self.logger = logging.getLogger('StoreService')

        # 加载配置
        self._load_config()

    def _load_config(self):
        """加载配置文件"""
        try:
            config_parser = configparser.ConfigParser()
            config_parser.read(self.config_path, encoding='utf-8')

            # 存储模块配置
            store_config = dict(config_parser.items('store')) if config_parser.has_section('store') else {}

            self.config = {
                # 数据库配置
                'db_path': store_config.get('db_path', 'store.db'),
                'max_connections': int(store_config.get('max_connections', '10')),

                # 目录监控配置
                'watch_directories': [d.strip() for d in store_config.get('watch_directories', './data/watch').split(';')],
                'zmq_publish_addr': store_config.get('zmq_publish_addr', 'tcp://0.0.0.0:13232'),

                # 网络订阅配置
                'cluster_nodes': [n.strip() for n in store_config.get('cluster_nodes', '').split(',') if n.strip()],
                'subscribe_topics': [t.strip() for t in store_config.get('subscribe_topics', 'original_data,feature_data').split(',')],

                # 数据处理配置
                'max_workers': int(store_config.get('max_workers', '4')),

                # 队列配置
                'queue_maxsize': int(store_config.get('queue_maxsize', '1000')),

                # 服务启用状态
                'service_enabled': store_config.get('service_enabled', 'true').lower() == 'true'
            }

            self.logger.info("配置加载完成")
            self.logger.info(f"  数据库路径: {self.config['db_path']}")
            self.logger.info(f"  监控目录: {self.config['watch_directories']}")
            self.logger.info(f"  集群节点: {self.config['cluster_nodes']}")
            self.logger.info(f"  工作线程数: {self.config['max_workers']}")

        except Exception as e:
            self.logger.error(f"加载配置失败: {e}")
            raise

    def _setup_components(self):
        """设置核心组件"""
        try:
            # 创建ZeroMQ上下文
            self.zmq_context = zmq.Context()

            # 创建数据库连接池
            self.connection_pool = StoreConnectionPool(
                db_path=self.config['db_path'],
                max_connections=self.config['max_connections']
            )

            # 创建内部消息队列
            self.message_queue = Queue(maxsize=self.config['queue_maxsize'])

            # 创建三个线程
            self.directory_monitor = DirectoryMonitorThread(
                config=self.config,
                message_queue=self.message_queue,
                zmq_context=self.zmq_context
            )

            self.network_subscriber = NetworkSubscriberThread(
                config=self.config,
                message_queue=self.message_queue,
                zmq_context=self.zmq_context
            )

            self.data_processor = DataProcessorThread(
                config=self.config,
                message_queue=self.message_queue,
                connection_pool=self.connection_pool
            )

            self.logger.info("核心组件设置完成")

        except Exception as e:
            self.logger.error(f"设置核心组件失败: {e}")
            raise

    def start(self):
        """启动存储服务"""
        try:
            if not self.config['service_enabled']:
                self.logger.info("存储服务已禁用")
                return

            self.logger.info("=" * 60)
            self.logger.info("🚀 启动分布式数据收集和存储服务")
            self.logger.info("=" * 60)

            self.running = True

            # 设置核心组件
            self._setup_components()

            # 按顺序启动三个线程
            self.logger.info("启动数据处理线程...")
            self.data_processor.start()

            self.logger.info("启动目录监控线程...")
            self.directory_monitor.start()

            if self.config['cluster_nodes']:
                self.logger.info("启动网络订阅线程...")
                self.network_subscriber.start()
            else:
                self.logger.info("未配置集群节点，跳过网络订阅线程")

            self.logger.info("=" * 60)
            self.logger.info("✅ 存储服务启动完成！")
            self.logger.info("=" * 60)
            self.logger.info("💡 按 Ctrl+C 可安全退出服务")
            self.logger.info("=" * 60)

        except Exception as e:
            self.logger.error(f"启动存储服务失败: {e}")
            self.stop()
            raise

    def stop(self):
        """停止存储服务"""
        try:
            if not self.running:
                return

            self.logger.info("=" * 60)
            self.logger.info("🛑 停止分布式数据收集和存储服务")
            self.logger.info("=" * 60)

            self.running = False
            self.stop_event.set()

            # 按相反顺序停止三个线程
            if self.network_subscriber:
                self.logger.info("停止网络订阅线程...")
                self.network_subscriber.stop()

            if self.directory_monitor:
                self.logger.info("停止目录监控线程...")
                self.directory_monitor.stop()

            if self.data_processor:
                self.logger.info("停止数据处理线程...")
                self.data_processor.stop()

            # 清理资源
            if self.connection_pool:
                self.connection_pool.close_all()

            if self.zmq_context:
                self.zmq_context.term()

            self.logger.info("=" * 60)
            self.logger.info("✅ 存储服务已安全停止")
            self.logger.info("=" * 60)

        except Exception as e:
            self.logger.error(f"停止存储服务时出错: {e}")

    def health_check(self) -> bool:
        """健康检查"""
        try:
            # 检查服务运行状态
            if not self.running:
                return False

            # 检查数据库连接
            if self.connection_pool:
                with self.connection_pool.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    cursor.fetchone()

            # 检查消息队列
            if self.message_queue and self.message_queue.qsize() > self.config['queue_maxsize'] * 0.9:
                self.logger.warning("消息队列接近满载")
                return False

            return True

        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
            return False


def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('store.log', encoding='utf-8')
        ]
    )


def main():
    """主函数"""
    import signal
    import sys

    # 设置日志
    setup_logging()

    # 创建存储服务
    store_service = StoreService()

    # 设置信号处理器
    def signal_handler(signum, frame):
        print(f"\n🛑 收到信号 {signum}，正在安全关闭存储服务...")
        store_service.stop()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        # 启动服务
        store_service.start()

        # 保持运行状态
        while store_service.running:
            time.sleep(1)

            # 定期健康检查（每30秒）
            if int(time.time()) % 30 == 0:
                if not store_service.health_check():
                    store_service.logger.warning("⚠️ 健康检查失败")

    except KeyboardInterrupt:
        print("\n🛑 收到中断信号，正在关闭服务...")
    except Exception as e:
        print(f"❌ 服务运行失败: {e}")
        store_service.logger.error(f"服务运行失败: {e}")
    finally:
        store_service.stop()


if __name__ == "__main__":
    main()
