#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分布式数据收集和存储模块 - 基于ZeroMQ、SQLite和Watchdog的三线程系统
实现两个生产者线程和一个消费者线程的分布式数据收集和存储功能

创建时间: 2025-01-25
"""

import configparser
import json
import logging
import os
import sqlite3
import time
import queue
import threading
import zmq
import re
from datetime import datetime
from dateutil import parser
from concurrent.futures import ThreadPoolExecutor
from contextlib import contextmanager
from queue import Queue, Empty
from threading import Lock, Event
from typing import Dict, Any, Optional, List, Tuple
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler


class StoreConnectionPool:
    """SQLite连接池管理器 - 专用于store模块"""
    
    def __init__(self, db_path: str, max_connections: int = 10):
        self.db_path = db_path
        self.max_connections = max_connections
        self.connections = Queue(maxsize=max_connections)
        self.lock = Lock()
        self.logger = logging.getLogger('StoreConnectionPool')
        self._initialize_pool()
        
    def _initialize_pool(self):
        """初始化连接池"""
        for _ in range(self.max_connections):
            conn = self._create_connection()
            if conn:
                self.connections.put(conn)
        self.logger.info(f"数据库连接池初始化完成，连接数: {self.max_connections}")
    
    def _create_connection(self) -> Optional[sqlite3.Connection]:
        """创建新的数据库连接"""
        try:
            conn = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=30.0
            )
            
            # 启用WAL模式和优化设置
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=-64000")
            conn.execute("PRAGMA temp_store=MEMORY")
            conn.execute("PRAGMA mmap_size=268435456")
            conn.execute("PRAGMA foreign_keys=ON")
            conn.execute("PRAGMA auto_vacuum=INCREMENTAL")
            conn.execute("PRAGMA busy_timeout=30000")
            
            conn.row_factory = sqlite3.Row
            return conn
            
        except Exception as e:
            self.logger.error(f"创建数据库连接失败: {e}")
            return None
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = None
        try:
            with self.lock:
                if not self.connections.empty():
                    conn = self.connections.get_nowait()
                else:
                    conn = self._create_connection()
                    
            if conn is None:
                raise Exception("无法获取数据库连接")
                
            yield conn
            
        except Exception as e:
            self.logger.error(f"数据库操作错误: {e}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                try:
                    conn.commit()
                    with self.lock:
                        if not self.connections.full():
                            self.connections.put(conn)
                        else:
                            conn.close()
                except Exception as e:
                    self.logger.error(f"归还连接时出错: {e}")
                    conn.close()
    
    def close_all(self):
        """关闭所有连接"""
        with self.lock:
            while not self.connections.empty():
                try:
                    conn = self.connections.get_nowait()
                    conn.close()
                except Exception as e:
                    self.logger.error(f"关闭连接时出错: {e}")


class FileStabilityChecker:
    """文件稳定性检测器 - 检测文件大小50ms内未变化"""
    
    def __init__(self, stability_time: float = 0.05):
        self.stability_time = stability_time  # 50ms
        self.logger = logging.getLogger('FileStabilityChecker')
    
    def is_file_stable(self, file_path: str) -> bool:
        """检查文件是否稳定（大小50ms内未变化）"""
        try:
            if not os.path.exists(file_path):
                return False
            
            # 获取初始文件大小
            initial_size = os.path.getsize(file_path)
            initial_mtime = os.path.getmtime(file_path)
            
            # 等待稳定时间
            time.sleep(self.stability_time)
            
            # 检查文件大小是否变化
            if not os.path.exists(file_path):
                return False
                
            current_size = os.path.getsize(file_path)
            current_mtime = os.path.getmtime(file_path)
            
            # 文件大小和修改时间都未变化则认为稳定
            is_stable = (initial_size == current_size and initial_mtime == current_mtime)
            
            if is_stable:
                self.logger.debug(f"文件稳定: {file_path}")
            else:
                self.logger.debug(f"文件不稳定: {file_path}")
                
            return is_stable
            
        except Exception as e:
            self.logger.error(f"检查文件稳定性失败 {file_path}: {e}")
            return False


class FilenameParser:
    """文件名解析器 - 解析 {id}_{type}_{timestamp}.json 格式"""
    
    def __init__(self):
        # 匹配 {id}_{type}_{timestamp}.json 格式
        self.pattern = re.compile(r'^(\d+)_([a-zA-Z0-9]+)_(\d+)\.json$')
        self.logger = logging.getLogger('FilenameParser')
    
    def parse_filename(self, filename: str) -> Optional[Dict[str, str]]:
        """解析文件名，返回 {id, type, timestamp} 或 None"""
        try:
            match = self.pattern.match(os.path.basename(filename))
            if match:
                file_id, file_type, timestamp = match.groups()
                
                # 确定数据类型
                data_type = "original_data" if file_type == "0" else "feature_data"
                
                return {
                    'id': file_id,
                    'type': file_type,
                    'data_type': data_type,
                    'timestamp': timestamp,
                    'filename': filename
                }
            else:
                self.logger.debug(f"文件名格式不匹配: {filename}")
                return None
                
        except Exception as e:
            self.logger.error(f"解析文件名失败 {filename}: {e}")
            return None


class DirectoryMonitorThread:
    """生产者线程1 - 目录监控器"""
    
    def __init__(self, config: Dict[str, Any], message_queue: Queue, zmq_context: zmq.Context):
        self.config = config
        self.message_queue = message_queue
        self.zmq_context = zmq_context
        self.publisher = None
        self.observer = None
        self.running = False
        self.stop_event = Event()
        
        # 配置参数
        self.watch_directories = config.get('watch_directories', [])
        self.zmq_publish_addr = config.get('zmq_publish_addr', 'tcp://0.0.0.0:13232')
        
        # 工具类
        self.stability_checker = FileStabilityChecker()
        self.filename_parser = FilenameParser()
        
        self.logger = logging.getLogger('DirectoryMonitor')
    
    def _setup_zmq_publisher(self):
        """设置ZeroMQ发布器"""
        try:
            self.publisher = self.zmq_context.socket(zmq.PUB)
            self.publisher.setsockopt(zmq.SNDHWM, 10000)
            self.publisher.setsockopt(zmq.LINGER, 1000)
            self.publisher.setsockopt(zmq.SNDBUF, 1024 * 1024)
            self.publisher.bind(self.zmq_publish_addr)
            self.logger.info(f"ZeroMQ发布器绑定到: {self.zmq_publish_addr}")
            return True
        except Exception as e:
            self.logger.error(f"设置ZeroMQ发布器失败: {e}")
            return False
    
    class FileEventHandler(FileSystemEventHandler):
        """文件系统事件处理器"""
        
        def __init__(self, monitor_thread):
            self.monitor = monitor_thread
            self.logger = logging.getLogger('FileEventHandler')
        
        def on_created(self, event):
            if not event.is_directory and event.src_path.endswith('.json'):
                self.monitor._handle_file_event(event.src_path)
        
        def on_modified(self, event):
            if not event.is_directory and event.src_path.endswith('.json'):
                self.monitor._handle_file_event(event.src_path)
    
    def _handle_file_event(self, file_path: str):
        """处理文件事件"""
        try:
            # 解析文件名
            parsed = self.filename_parser.parse_filename(file_path)
            if not parsed:
                return
            
            # 等待文件稳定
            max_attempts = 3
            for attempt in range(max_attempts):
                if self.stability_checker.is_file_stable(file_path):
                    break  # 文件已稳定，跳出循环
                self.logger.debug(f"文件不稳定，正在进行第 {attempt + 1} 次检查: {file_path}")
            
            # 读取文件内容
            with open(file_path, 'rb') as f:
                file_content = f.read()
            
            # 发布到ZeroMQ
            topic = parsed['data_type']
            message = [topic.encode(), os.path.basename(file_path).encode(), file_content]
            
            if self.publisher:
                self.publisher.send_multipart(message)
                self.logger.info(f"发布文件到ZeroMQ: {file_path} (topic: {topic})")
            
            # 放入内部消息队列
            queue_message = (os.path.basename(file_path), file_content)
            self.message_queue.put(queue_message)
            self.logger.debug(f"文件放入内部队列: {file_path}")
            
        except Exception as e:
            self.logger.error(f"处理文件事件失败 {file_path}: {e}")
    
    def start(self):
        """启动目录监控线程"""
        try:
            self.running = True
            
            # 设置ZeroMQ发布器
            if not self._setup_zmq_publisher():
                raise Exception("ZeroMQ发布器设置失败")
            
            # 设置文件监控
            self.observer = Observer()
            event_handler = self.FileEventHandler(self)
            
            for directory in self.watch_directories:
                if os.path.exists(directory):
                    self.observer.schedule(event_handler, directory, recursive=True)
                    self.logger.info(f"开始监控目录: {directory}")
                else:
                    self.logger.warning(f"监控目录不存在: {directory}")
            
            self.observer.start()
            self.logger.info("目录监控线程启动成功")
            
        except Exception as e:
            self.logger.error(f"启动目录监控线程失败: {e}")
            raise
    
    def stop(self):
        """停止目录监控线程"""
        self.running = False
        self.stop_event.set()
        
        if self.observer:
            self.observer.stop()
            self.observer.join()
        
        if self.publisher:
            self.publisher.close()
        
        self.logger.info("目录监控线程已停止")


class NetworkSubscriberThread:
    """生产者线程2 - 网络订阅器"""

    def __init__(self, config: Dict[str, Any], message_queue: Queue, zmq_context: zmq.Context):
        self.config = config
        self.message_queue = message_queue
        self.zmq_context = zmq_context
        self.subscribers = []
        self.running = False
        self.stop_event = Event()
        self.worker_threads = []

        # 配置参数
        self.cluster_nodes = config.get('cluster_nodes', [])
        self.subscribe_topics = config.get('subscribe_topics', ['original_data', 'feature_data'])

        self.logger = logging.getLogger('NetworkSubscriber')

    def _create_subscriber(self, endpoint: str) -> zmq.Socket:
        """创建ZeroMQ订阅器"""
        try:
            subscriber = self.zmq_context.socket(zmq.SUB)
            subscriber.setsockopt(zmq.RCVHWM, 10000)
            subscriber.setsockopt(zmq.LINGER, 1000)
            subscriber.setsockopt(zmq.RCVBUF, 1024 * 1024)

            # 订阅指定主题
            for topic in self.subscribe_topics:
                subscriber.setsockopt_string(zmq.SUBSCRIBE, topic)
                self.logger.debug(f"订阅主题: {topic} 从 {endpoint}")

            subscriber.connect(endpoint)
            self.logger.info(f"连接到ZeroMQ端点: {endpoint}")
            return subscriber

        except Exception as e:
            self.logger.error(f"创建订阅器失败 {endpoint}: {e}")
            return None

    def _subscriber_worker(self, endpoint: str):
        """订阅器工作线程"""
        subscriber = self._create_subscriber(endpoint)
        if not subscriber:
            return

        self.logger.info(f"订阅器工作线程启动: {endpoint}")

        try:
            while self.running and not self.stop_event.is_set():
                try:
                    # 非阻塞接收消息
                    if subscriber.poll(1000):  # 1秒超时
                        message_parts = subscriber.recv_multipart(zmq.NOBLOCK)

                        if len(message_parts) >= 3:
                            topic = message_parts[0].decode()
                            filename = message_parts[1].decode()
                            file_content = message_parts[2]

                            # 根据主题处理消息
                            self._handle_received_message(topic, filename, file_content, endpoint)

                except zmq.Again:
                    # 没有消息可接收，继续循环
                    continue
                except Exception as e:
                    self.logger.error(f"接收消息失败 {endpoint}: {e}")
                    time.sleep(1)

        except Exception as e:
            self.logger.error(f"订阅器工作线程异常 {endpoint}: {e}")
        finally:
            if subscriber:
                subscriber.close()
            self.logger.info(f"订阅器工作线程退出: {endpoint}")

    def _handle_received_message(self, topic: str, filename: str, file_content: bytes, endpoint: str):
        """处理接收到的消息"""
        try:
            # 根据主题进行不同处理
            if topic == 'original_data':
                self.logger.debug(f"接收到原始数据: {filename} 从 {endpoint}")
            elif topic == 'feature_data':
                self.logger.debug(f"接收到特征数据: {filename} 从 {endpoint}")
            else:
                self.logger.warning(f"未知主题: {topic} 从 {endpoint}")
                return

            # 放入内部消息队列
            queue_message = (filename, file_content)
            self.message_queue.put(queue_message)
            self.logger.debug(f"网络消息放入内部队列: {filename}")

        except Exception as e:
            self.logger.error(f"处理接收消息失败 {filename}: {e}")

    def start(self):
        """启动网络订阅线程"""
        try:
            self.running = True

            # 为每个集群节点创建订阅器工作线程
            for node_ip in self.cluster_nodes:
                endpoint = f"tcp://{node_ip}:13232"
                worker_thread = threading.Thread(
                    target=self._subscriber_worker,
                    args=(endpoint,),
                    name=f"Subscriber-{node_ip}",
                    daemon=True
                )
                worker_thread.start()
                self.worker_threads.append(worker_thread)

            self.logger.info(f"网络订阅线程启动成功，订阅 {len(self.cluster_nodes)} 个节点")

        except Exception as e:
            self.logger.error(f"启动网络订阅线程失败: {e}")
            raise

    def stop(self):
        """停止网络订阅线程"""
        self.running = False
        self.stop_event.set()

        # 等待所有工作线程结束
        for thread in self.worker_threads:
            thread.join(timeout=5)
            if thread.is_alive():
                self.logger.warning(f"订阅器线程 {thread.name} 未能正常退出")

        self.logger.info("网络订阅线程已停止")


class DataProcessorThread:
    """消费者线程 - 数据处理器"""

    def __init__(self, config: Dict[str, Any], message_queue: Queue, connection_pool: StoreConnectionPool):
        self.config = config
        self.message_queue = message_queue
        self.connection_pool = connection_pool
        self.running = False
        self.stop_event = Event()

        # 线程池配置
        self.max_workers = config.get('max_workers', 4)
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)

        # 工具类
        self.filename_parser = FilenameParser()

        self.logger = logging.getLogger('DataProcessor')

    def _setup_database_tables(self):
        """设置数据库表结构 - 与database.py保持一致"""
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()

                # 创建源数据表，保存数据的输入信息-插入操作（与database.py完全一致）
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS origional_table (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sat_id TEXT,
                        sat_category TEXT,
                        sat_load_category TEXT,
                        planed_start_time TEXT,
                        planed_end_time TEXT,
                        file_size REAL,
                        file_name TEXT,
                        file_path TEXT,
                        image_truth_width REAL,
                        image_resolution REAL,
                        timestamp TEXT NOT NULL,
                        enu_base_longitude REAL,
                        enu_base_latitude REAL,
                        enu_base_altitude REAL,
                        sat_enu_x REAL,
                        sat_enu_y REAL,
                        sat_enu_z REAL,
                        cam_enu_x REAL,
                        cam_enu_y REAL,
                        cam_enu_z REAL,
                        cam_enu_w REAL,
                        sat_j2000_x REAL,
                        sat_j2000_y REAL,
                        sat_j2000_z REAL,
                        sat_qbi_x REAL,
                        sat_qbi_y REAL,
                        sat_qbi_z REAL,
                        sat_qbi_w REAL,
                        load_fov_x REAL,
                        load_fov_y REAL,
                        image_pixel_x REAL,
                        image_pixel_y REAL
                    )
                ''')

                # 创建真值表-插入操作（与database.py完全一致）
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS truth_table (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sat_id TEXT,
                        timestamp TEXT NOT NULL,
                        target_id TEXT,
                        fleet_number TEXT,
                        target_category TEXT,
                        target_direction TEXT,
                        target_longitude REAL,
                        target_latitude REAL,
                        target_altitude REAL,
                        target_sat_z REAL,
                        target_sat_y REAL,
                        target_sat_x REAL,
                        target_x REAL,
                        target_y REAL,
                        target_width REAL,
                        target_height REAL
                    )
                ''')

                # 创建特征表，保存算法的中间结果信息--插入+更新操作（与database.py完全一致）
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS feature_table (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sat_id TEXT NOT NULL,
                        fleet_number TEXT,
                        target_category TEXT,
                        target_id TEXT,
                        timestamp TEXT NOT NULL,
                        target_image_path TEXT,
                        target_truth BOOL,
                        target_x REAL,
                        target_y REAL,
                        target_width REAL,
                        target_height REAL,
                        target_longitude REAL,
                        target_latitude REAL,
                        target_fusion_longitude REAL,
                        target_fusion_latitude REAL,
                        target_longitude_speed REAL,
                        target_latitude_speed REAL,
                        target_total_speed REAL,
                        target_npy_path TEXT
                    )
                ''')

                # 创建决策表，保存数据的决策信息--只有插入操作（与database.py完全一致）
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS decision_table (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT,
                        query_days TEXT,
                        fleet_number TEXT,
                        speed_threat REAL,
                        heading_threat REAL,
                        distance_threat REAL,
                        heading_angle REAL,
                        speed REAL,
                        lat REAL,
                        lon REAL,
                        intention TEXT
                    )
                ''')

                self.logger.info("数据库表结构设置完成")

        except Exception as e:
            self.logger.error(f"设置数据库表结构失败: {e}")
            raise

    def _process_message(self, message: Tuple):
        """处理单个消息 - 使用与database.py一致的逻辑"""
        try:
            if len(message) == 3:
                # 来自目录监控的消息格式: (topic, filename, file_content_bytes)
                _, filename, file_content = message
            elif len(message) == 2:
                # 来自网络订阅的消息格式: (filename, file_content_bytes)
                filename, file_content = message
            else:
                self.logger.error(f"无效的消息格式: {message}")
                return

            self.logger.info(f"开始处理文件: {filename}")

            # 解析任务信息 - 使用database.py的逻辑
            task_info = self._parse_filename(filename)
            if not task_info:
                self.logger.debug(f"临时文件，跳过解析存储: {filename}")
                return

            task_category = task_info['task_category']
            file_ext = task_info['file_ext']

            # 如果文件名的任务类别为"0"，并且后缀为jpg，就不保存文件
            if self._is_origin_data(task_category, file_ext):
                self.logger.debug(f"原始图像，执行语义化数据解析: {filename}")
                return

            # 解析并存储数据 - 使用database.py的逻辑
            self._parse_and_store_data(task_info, file_content, filename)

        except Exception as e:
            self.logger.error(f"处理消息失败 {filename}: {e}")

    def _parse_filename(self, filename: str) -> Dict[str, str]:
        """
        解析文件名格式：卫星id_任务类型_时间戳.ext - 与database.py完全一致

        :param filename: 文件名
        :return: 解析后的任务信息字典
        """
        try:
            base_name = os.path.splitext(filename)[0]
            file_ext = os.path.splitext(filename)[1].lower()
            parts = base_name.split('_')

            if len(parts) < 3:
                self.logger.debug("文件名解析完成，不满足三部分要求")
                return None

            self.logger.debug(f"文件名解析成功，sat_id={parts[0]},task_info={parts[1]},timestamp={parts[2]}")
            sat_id = parts[0]
            task_category = parts[1]
            try:
                dt = parser.parse(parts[2])
                # 处理无时区的情况（视为UTC）
                if dt.tzinfo is None:
                    import datetime as dt_module
                    dt = dt.replace(tzinfo=dt_module.timezone.utc)
                # 转换为Unix时间戳（秒）后转毫秒
                timestamp = int(dt.timestamp() * 1000)
            except Exception as e:
                timestamp = parts[2]

            return {
                'filename': filename,
                'sat_id': sat_id,
                'task_category': str(task_category),
                'timestamp': timestamp,
                'file_ext': file_ext
            }

        except Exception as e:
            self.logger.error(f"解析文件名失败: {filename} - {e}")
            return None

    def _is_origin_data(self, task_category: str, file_ext: str) -> bool:
        """判断是否为源图像数据 - 与database.py完全一致"""
        # 任务类别为"0"，文件后缀为.jpg
        return task_category == '0' and file_ext ==  '.jpg'

    def _parse_and_store_data(self, task_info: dict, content: bytes, file_path: str):
        """解析并存储任务数据 - 与database.py完全一致"""
        try:
            if task_info['task_category'] == '0' and task_info['file_ext'] == '.json':
                self._store_origin_data(content, task_info)
            elif task_info['task_category'] == 'BHSF' and task_info['file_ext'] == '.json':
                self._store_feature_data_BHSF(content, task_info)
            elif task_info['task_category'] == 'WZZH' and task_info['file_ext'] == '.json':
                self._store_feature_data_WZZH(content, task_info)
            elif task_info['task_category'] == 'RHDW' and task_info['file_ext'] == '.json':
                self._store_feature_data_RHDW(content, task_info)
            elif task_info['task_category'] == 'XDSF' and task_info['file_ext'] == '.json':
                self._store_feature_data_XDSF(content, task_info)
            elif task_info['task_category'] == 'BKSF' and task_info['file_ext'] == '.json':
                self._store_feature_data_BKSF(content, task_info)
            elif task_info['task_category'] == 'FXJG' and task_info['file_ext'] == '.json':
                self._store_decision_data(content, task_info)
            else:
                self.logger.warning(f"未知的任务类别或文件类型: {task_info['task_category']} {task_info['file_ext']}")
        except Exception as e:
            self.logger.error(f"任务数据存储失败: {file_path} - {e}")

    def _store_origin_data(self, content: bytes, task_info: Dict[str, str]):
        """
        存储源数据到origional_table和truth_table表 - 与database.py完全一致

        :param content: 文件内容
        :param task_info: 任务信息
        """
        try:
            filename = task_info['filename']
            # 解析JSON内容
            json_data = json.loads(content.decode('utf-8'))

            device_info = json_data.get('deviceInfo', {})
            selected_image = json_data.get('selectedImage', {})

            # 根据task.txt中的映射关系进行类型转换
            device_type_map = {0: 'GEO', 131072: 'LEO', 131073: 'LEO_SAR', 131074: 'LEO_RADAR', 131075: 'LEO_ELECTRO'}
            equip_type_map = {1: 'CCD', 2: 'IR', 3: 'SAR', 4: 'RADAR', 5: 'ELECTRO', 6: 'PAN'}
            target_type_map = {
                101: 'AIRPORT', 102: 'PORT', 1201: 'CARRIER', 1202: 'BATTLESHIP',
                1203: 'DESTROYER', 1204: 'SUPPLY', 1205: 'FRIGATE'
            }

            # 提取基本信息用于origional_table
            origin_data = {
                'sat_id': str(task_info.get('sat_id', '')),
                'sat_category': device_type_map.get(device_info.get('deviceType', 0), 'OTHER'),
                'sat_load_category': equip_type_map.get(device_info.get('equipType', 0), 'OTHER'),
                'planed_start_time': str(device_info.get('planedStartTime', 0)),
                'planed_end_time': str(device_info.get('planedEndTime', 0)),
                'timestamp': str(task_info.get('timestamp', 0)),

                # 文件相关信息
                'file_size': selected_image.get('imageSize', 0.0),
                'file_name': selected_image.get('imageName', filename),
                'file_path': selected_image.get('imagePath', ''),
                'image_truth_width': selected_image.get('image_truth_width', 0.0),
                'image_resolution': selected_image.get('image_resolution', 0.0),

                # 视线中心点ENU坐标系信息
                'enu_base_longitude': selected_image.get('enuBaseLon', 0.0),
                'enu_base_latitude': selected_image.get('enuBaseLat', 0.0),
                'enu_base_altitude': selected_image.get('enuBaseAlt', 0.0),

                # 卫星位置信息
                'sat_enu_x': selected_image.get('satPosEnuX', 0.0),
                'sat_enu_y': selected_image.get('satPosEnuY', 0.0),
                'sat_enu_z': selected_image.get('satPosEnuZ', 0.0),

                # 相机四元数信息
                'cam_enu_x': selected_image.get('camQENU_X', 0.0),
                'cam_enu_y': selected_image.get('camQENU_Y', 0.0),
                'cam_enu_z': selected_image.get('camQENU_Z', 0.0),
                'cam_enu_w': selected_image.get('camQENU_W', 0.0),

                # J2000坐标系信息
                'sat_j2000_x': selected_image.get('satPosJ2000X', 0.0),
                'sat_j2000_y': selected_image.get('satPosJ2000Y', 0.0),
                'sat_j2000_z': selected_image.get('satPosJ2000Z', 0.0),

                # 四元数信息
                'sat_qbi_x': selected_image.get('satQbiX', 0.0),
                'sat_qbi_y': selected_image.get('satQbiY', 0.0),
                'sat_qbi_z': selected_image.get('satQbiZ', 0.0),
                'sat_qbi_w': selected_image.get('satQbiW', 0.0),

                # 载荷视场角
                'load_fov_x': selected_image.get('fovX', 0.0),
                'load_fov_y': selected_image.get('fovY', 0.0),

                # 图像像素信息
                'image_pixel_x': selected_image.get('camPixelX', 0.0),
                'image_pixel_y': selected_image.get('camPixelY', 0.0)
            }

            # 插入origional_table数据
            origin_record_id = self._insert_origin_data(origin_data)
            if origin_record_id:
                self.logger.info(f"成功插入源数据，记录ID: {origin_record_id}")
            else:
                self.logger.error(f"插入源数据失败: {filename}")
                return

            # 处理目标信息，插入truth_table
            target_info_list = selected_image.get('targetInfo', [])
            target_num = selected_image.get('targetNum', 0)

            if target_num > 0 and target_info_list:
                for i, target_info in enumerate(target_info_list[:target_num]):
                    # 提取目标信息
                    drawbox = target_info.get('drawbox', {})
                    returnbox = target_info.get('returnbox', {})

                    truth_data = {
                        'sat_id': str(task_info.get('sat_id', '')),
                        'timestamp': str(task_info.get('timestamp', 0)),
                        'target_id': str(target_info.get('targetID', '')),
                        'fleet_number': str(target_info.get('targetFleetNumber', '')),
                        'target_category': target_type_map.get(target_info.get('targetType', 0), 'OTHER'),
                        'target_direction': str(target_info.get('targetDirection', 0.0)),

                        # 目标位置信息
                        'target_longitude': target_info.get('targetPosLon', 0.0),
                        'target_latitude': target_info.get('targetPosLat', 0.0),
                        'target_altitude': target_info.get('targetPosAlt', 0.0),

                        # 卫星坐标系下的目标位置
                        'target_sat_x': target_info.get('targetPosInSatX', 0.0),
                        'target_sat_y': target_info.get('targetPosInSatY', 0.0),
                        'target_sat_z': target_info.get('targetPosInSatZ', 0.0),

                        # 目标像素位置信息
                        'target_x': returnbox.get('x', 0.0),
                        'target_y': returnbox.get('y', 0.0),
                        'target_width': returnbox.get('width', 0.0),
                        'target_height': returnbox.get('height', 0.0)
                    }

                    # 插入truth_table数据
                    truth_record_id = self._insert_truth_data(truth_data)
                    if truth_record_id:
                        self.logger.debug(f"成功插入目标数据 {i+1}/{target_num}，记录ID: {truth_record_id}")
                    else:
                        self.logger.error(f"插入目标数据失败: {filename}, 目标 {i+1}")

                self.logger.info(f"处理了 {len(target_info_list[:target_num])} 个目标信息")
            else:
                self.logger.info(f"文件 {filename} 中没有目标信息")

            self.logger.info(f"源数据已成功存储到数据库: {filename}")

        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败 {filename}: {e}")
        except Exception as e:
            self.logger.error(f"存储源数据失败 {filename}: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")

    def _insert_origin_data(self, origin_data: Dict[str, Any]) -> Optional[int]:
        """插入原始数据到origional_table"""
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()

                # 构建SQL语句
                columns = ', '.join(origin_data.keys())
                placeholders = ', '.join([f':{key}' for key in origin_data.keys()])
                sql = f"INSERT INTO origional_table ({columns}) VALUES ({placeholders})"

                cursor.execute(sql, origin_data)
                return cursor.lastrowid

        except Exception as e:
            self.logger.error(f"插入原始数据失败: {e}")
            return None

    def _insert_truth_data(self, truth_data: Dict[str, Any]) -> Optional[int]:
        """插入真值数据到truth_table"""
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()

                # 构建SQL语句
                columns = ', '.join(truth_data.keys())
                placeholders = ', '.join([f':{key}' for key in truth_data.keys()])
                sql = f"INSERT INTO truth_table ({columns}) VALUES ({placeholders})"

                cursor.execute(sql, truth_data)
                return cursor.lastrowid

        except Exception as e:
            self.logger.error(f"插入真值数据失败: {e}")
            return None

    def _insert_feature_data(self, feature_data: Dict[str, Any]) -> Optional[int]:
        """插入特征数据到feature_table"""
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()

                # 构建SQL语句
                columns = ', '.join(feature_data.keys())
                placeholders = ', '.join([f':{key}' for key in feature_data.keys()])
                sql = f"INSERT INTO feature_table ({columns}) VALUES ({placeholders})"

                cursor.execute(sql, feature_data)
                return cursor.lastrowid

        except Exception as e:
            self.logger.error(f"插入特征数据失败: {e}")
            return None

    def _store_feature_data_BHSF(self, content: bytes, task_info: Dict[str, str]):
        """
        存储特征数据到feature_table表 - 与database.py完全一致

        :param content: 文件内容
        :param task_info: 任务信息
        """
        try:
            # 目标类型映射
            target_type_map = {
                101: 'AIRPORT', 102: 'PORT', 1201: 'CARRIER', 1202: 'BATTLESHIP',
                1203: 'DESTROYER', 1204: 'SUPPLY', 1205: 'FRIGATE'
            }
            filename = task_info['filename']
            json_data = json.loads(content.decode('utf-8'))
            for data_item in json_data:
                target_image_path = data_item.get('image_path','')
                target_id = data_item.get('target_id')
                target_category = data_item.get('type')
                target_truth = data_item.get('truth')
                position = data_item.get('position', {})
                target_x = position.get('x1', 0)
                target_y = position.get('y1', 0)
                target_width = position.get('width', 0)
                target_height = position.get('height', 0)

                feature_data = {
                    'sat_id': task_info['sat_id'],
                    'fleet_number': 0,
                    'timestamp': task_info['timestamp'],
                    'target_category': target_type_map.get(target_category, 'OTHER'),
                    'target_id': target_id,
                    'target_image_path': target_image_path,
                    'target_truth': target_truth,
                    'target_x': target_x,
                    'target_y': target_y,
                    'target_width': target_width,
                    'target_height': target_height
                }

                record_id = self._insert_feature_data(feature_data)
                if record_id:
                    self.logger.info(f"身份识别数据存储成功: target_id={feature_data['target_id']}, "
                                    f"type={feature_data['target_category']}, truth={feature_data['target_truth']} (ID: {record_id})")

        except (json.JSONDecodeError, ValueError) as e:
            self.logger.error(f"解析JSON特征数据失败: {filename} - {e}")

    def _store_feature_data_WZZH(self, content: bytes, task_info: Dict[str, str]):
        """存储位置转换特征数据 - 与database.py完全一致"""
        try:
            filename = task_info['filename']
            json_data = json.loads(content.decode('utf-8'))

            for target_data in json_data.get('targets', []):
                target_id = target_data.get('target_id')
                target_longitude = target_data.get('longitude')
                target_latitude = target_data.get('latitude')

                # 更新feature_table中对应记录的经纬度信息
                with self.connection_pool.get_connection() as conn:
                    cursor = conn.cursor()

                    update_sql = """
                    UPDATE feature_table
                    SET target_longitude = ?, target_latitude = ?
                    WHERE sat_id = ? AND target_id = ? AND timestamp = ?
                    """

                    cursor.execute(update_sql, [
                        target_longitude, target_latitude,
                        task_info['sat_id'], target_id, task_info['timestamp']
                    ])

                    if cursor.rowcount > 0:
                        self.logger.info(f"位置转换数据更新成功: target_id={target_id}, "
                                        f"lon={target_longitude}, lat={target_latitude}")
                    else:
                        self.logger.warning(f"未找到匹配的记录进行位置更新: target_id={target_id}")

        except (json.JSONDecodeError, ValueError) as e:
            self.logger.error(f"解析位置转换数据失败: {filename} - {e}")

    def _store_feature_data_RHDW(self, content: bytes, task_info: Dict[str, str]):
        """存储融合定位特征数据 - 与database.py完全一致"""
        try:
            filename = task_info['filename']
            json_data = json.loads(content.decode('utf-8'))

            for observation in json_data.get('observations', []):
                target_id = observation.get('target_id')
                fusion_longitude = observation.get('lon')
                fusion_latitude = observation.get('lat')

                # 更新feature_table中对应记录的融合定位信息
                with self.connection_pool.get_connection() as conn:
                    cursor = conn.cursor()

                    update_sql = """
                    UPDATE feature_table
                    SET target_fusion_longitude = ?, target_fusion_latitude = ?
                    WHERE sat_id = ? AND target_id = ? AND timestamp = ?
                    """

                    cursor.execute(update_sql, [
                        fusion_longitude, fusion_latitude,
                        task_info['sat_id'], target_id, task_info['timestamp']
                    ])

                    if cursor.rowcount > 0:
                        self.logger.info(f"融合定位数据更新成功: target_id={target_id}, "
                                        f"fusion_lon={fusion_longitude}, fusion_lat={fusion_latitude}")
                    else:
                        self.logger.warning(f"未找到匹配的记录进行融合定位更新: target_id={target_id}")

        except (json.JSONDecodeError, ValueError) as e:
            self.logger.error(f"解析融合定位数据失败: {filename} - {e}")

    def _store_feature_data_XDSF(self, content: bytes, task_info: Dict[str, str]):
        """存储雷达智能特征数据 - 与database.py完全一致"""
        try:
            filename = task_info['filename']
            json_data = json.loads(content.decode('utf-8'))

            for target_data in json_data.get('targets', []):
                target_id = target_data.get('target_id')
                target_npy_path = target_data.get('npy_path')

                # 更新feature_table中对应记录的npy路径信息
                with self.connection_pool.get_connection() as conn:
                    cursor = conn.cursor()

                    update_sql = """
                    UPDATE feature_table
                    SET target_npy_path = ?
                    WHERE sat_id = ? AND target_id = ? AND timestamp = ?
                    """

                    cursor.execute(update_sql, [
                        target_npy_path,
                        task_info['sat_id'], target_id, task_info['timestamp']
                    ])

                    if cursor.rowcount > 0:
                        self.logger.info(f"雷达智能数据更新成功: target_id={target_id}, npy_path={target_npy_path}")
                    else:
                        self.logger.warning(f"未找到匹配的记录进行雷达智能更新: target_id={target_id}")

        except (json.JSONDecodeError, ValueError) as e:
            self.logger.error(f"解析雷达智能数据失败: {filename} - {e}")

    def _store_feature_data_BKSF(self, content: bytes, task_info: Dict[str, str]):
        """存储电侦智能特征数据 - 与database.py完全一致"""
        try:
            filename = task_info['filename']
            json_data = json.loads(content.decode('utf-8'))

            for target_data in json_data.get('targets', []):
                target_id = target_data.get('target_id')
                longitude_speed = target_data.get('longitude_speed')
                latitude_speed = target_data.get('latitude_speed')
                total_speed = target_data.get('total_speed')

                # 更新feature_table中对应记录的速度信息
                with self.connection_pool.get_connection() as conn:
                    cursor = conn.cursor()

                    update_sql = """
                    UPDATE feature_table
                    SET target_longitude_speed = ?, target_latitude_speed = ?, target_total_speed = ?
                    WHERE sat_id = ? AND target_id = ? AND timestamp = ?
                    """

                    cursor.execute(update_sql, [
                        longitude_speed, latitude_speed, total_speed,
                        task_info['sat_id'], target_id, task_info['timestamp']
                    ])

                    if cursor.rowcount > 0:
                        self.logger.info(f"电侦智能数据更新成功: target_id={target_id}, "
                                        f"speeds=({longitude_speed}, {latitude_speed}, {total_speed})")
                    else:
                        self.logger.warning(f"未找到匹配的记录进行电侦智能更新: target_id={target_id}")

        except (json.JSONDecodeError, ValueError) as e:
            self.logger.error(f"解析电侦智能数据失败: {filename} - {e}")

    def _store_decision_data(self, content: bytes, task_info: Dict[str, str]):
        """存储决策数据到decision_table表 - 与database.py完全一致"""
        try:
            filename = task_info['filename']
            json_data = json.loads(content.decode('utf-8'))

            query_days = json_data.get('query_days', '')

            # 处理每个舰队的数据
            for fleet_key in ['FLEET_01', 'FLEET_02', 'FLEET_03']:
                fleet_data = json_data.get(fleet_key, {})

                speed_threat = fleet_data.get('speed_threat', 0.0)
                heading_threat = fleet_data.get('heading_threat', 0.0)
                distance_threat = fleet_data.get('distance_threat', 0.0)
                heading_angle = fleet_data.get('heading_angle', 0.0)
                speed = fleet_data.get('speed', 0.0)
                lat = fleet_data.get('lat', 0.0)
                lon = fleet_data.get('lon', 0.0)
                intention = fleet_data.get('intention', '')

                decision_data = {
                    'timestamp': task_info['timestamp'],
                    'query_days': query_days,
                    'fleet_number': fleet_key,
                    'speed_threat': speed_threat,
                    'heading_threat': heading_threat,
                    'distance_threat': distance_threat,
                    'heading_angle': heading_angle,
                    'speed': speed,
                    'lat': lat,
                    'lon': lon,
                    'intention': intention
                }

                # 插入决策数据
                with self.connection_pool.get_connection() as conn:
                    cursor = conn.cursor()

                    columns = ', '.join(decision_data.keys())
                    placeholders = ', '.join([f':{key}' for key in decision_data.keys()])
                    sql = f"INSERT INTO decision_table ({columns}) VALUES ({placeholders})"

                    cursor.execute(sql, decision_data)
                    record_id = cursor.lastrowid

                    if record_id:
                        self.logger.info(f"决策数据存储成功: {fleet_key}, intention={intention} (ID: {record_id})")
                    else:
                        self.logger.error(f"决策数据存储失败: {fleet_key}")

        except (json.JSONDecodeError, ValueError) as e:
            self.logger.error(f"解析决策数据失败: {filename} - {e}")

    def _consumer_worker(self):
        """消费者工作线程"""
        self.logger.info(f"数据处理工作线程启动: {threading.current_thread().name}")

        while self.running and not self.stop_event.is_set():
            try:
                # 从队列获取消息，最多等待1秒
                message = self.message_queue.get(timeout=1)

                # 处理特殊停止信号
                if message is None:
                    self.logger.debug("收到停止信号，消费者线程退出")
                    break

                # 提交到线程池处理
                future = self.executor.submit(self._process_message, message)

                # 标记任务完成
                self.message_queue.task_done()

            except Empty:
                # 队列为空，继续等待
                continue
            except Exception as e:
                self.logger.error(f"消费者工作线程异常: {e}")
                # 即使处理失败，也标记任务完成，避免队列阻塞
                if not self.message_queue.empty():
                    self.message_queue.task_done()

        self.logger.info(f"数据处理工作线程退出: {threading.current_thread().name}")

    def start(self):
        """启动数据处理线程"""
        try:
            self.running = True

            # 设置数据库表结构
            self._setup_database_tables()

            # 启动消费者工作线程
            self.worker_thread = threading.Thread(
                target=self._consumer_worker,
                name="DataProcessor-Worker",
                daemon=True
            )
            self.worker_thread.start()

            self.logger.info(f"数据处理线程启动成功，线程池大小: {self.max_workers}")

        except Exception as e:
            self.logger.error(f"启动数据处理线程失败: {e}")
            raise

    def stop(self):
        """停止数据处理线程"""
        self.running = False
        self.stop_event.set()

        # 发送停止信号
        try:
            self.message_queue.put(None, timeout=5)
        except:
            pass

        # 等待工作线程结束
        if hasattr(self, 'worker_thread'):
            self.worker_thread.join(timeout=10)
            if self.worker_thread.is_alive():
                self.logger.warning("数据处理工作线程未能正常退出")

        # 关闭线程池
        self.executor.shutdown(wait=True)

        self.logger.info("数据处理线程已停止")

class StoreService:
    """分布式数据收集和存储服务主类"""

    def __init__(self, config_path: str = "config.ini"):
        self.config_path = config_path
        self.config = {}
        self.running = False
        self.stop_event = Event()

        # 核心组件
        self.zmq_context = None
        self.connection_pool = None
        self.message_queue = None

        # 三个线程
        self.directory_monitor = None
        self.network_subscriber = None
        self.data_processor = None

        self.logger = logging.getLogger('StoreService')

        # 加载配置
        self._load_config()

    def _load_config(self):
        """加载配置文件"""
        try:
            config_parser = configparser.ConfigParser()
            config_parser.read(self.config_path, encoding='utf-8')

            # 存储模块配置
            store_config = dict(config_parser.items('store')) if config_parser.has_section('store') else {}

            self.config = {
                # 数据库配置
                'db_path': store_config.get('db_path', 'store.db'),
                'max_connections': int(store_config.get('max_connections', '10')),

                # 目录监控配置
                'watch_directories': [d.strip() for d in store_config.get('watch_directories', './data/watch').split(';')],
                'zmq_publish_addr': store_config.get('zmq_publish_addr', 'tcp://0.0.0.0:13232'),

                # 网络订阅配置
                'cluster_nodes': [n.strip() for n in store_config.get('cluster_nodes', '').split(',') if n.strip()],
                'subscribe_topics': [t.strip() for t in store_config.get('subscribe_topics', 'original_data,feature_data').split(',')],

                # 数据处理配置
                'max_workers': int(store_config.get('max_workers', '4')),

                # 队列配置
                'queue_maxsize': int(store_config.get('queue_maxsize', '1000')),

                # 服务启用状态
                'service_enabled': store_config.get('service_enabled', 'true').lower() == 'true'
            }

            self.logger.info("配置加载完成")
            self.logger.info(f"  数据库路径: {self.config['db_path']}")
            self.logger.info(f"  监控目录: {self.config['watch_directories']}")
            self.logger.info(f"  集群节点: {self.config['cluster_nodes']}")
            self.logger.info(f"  工作线程数: {self.config['max_workers']}")

        except Exception as e:
            self.logger.error(f"加载配置失败: {e}")
            raise

    def _setup_components(self):
        """设置核心组件"""
        try:
            # 创建ZeroMQ上下文
            self.zmq_context = zmq.Context()

            # 创建数据库连接池
            self.connection_pool = StoreConnectionPool(
                db_path=self.config['db_path'],
                max_connections=self.config['max_connections']
            )

            # 创建内部消息队列
            self.message_queue = Queue(maxsize=self.config['queue_maxsize'])

            # 创建三个线程
            self.directory_monitor = DirectoryMonitorThread(
                config=self.config,
                message_queue=self.message_queue,
                zmq_context=self.zmq_context
            )

            self.network_subscriber = NetworkSubscriberThread(
                config=self.config,
                message_queue=self.message_queue,
                zmq_context=self.zmq_context
            )

            self.data_processor = DataProcessorThread(
                config=self.config,
                message_queue=self.message_queue,
                connection_pool=self.connection_pool
            )

            self.logger.info("核心组件设置完成")

        except Exception as e:
            self.logger.error(f"设置核心组件失败: {e}")
            raise

    def start(self):
        """启动存储服务"""
        try:
            if not self.config['service_enabled']:
                self.logger.info("存储服务已禁用")
                return

            self.logger.info("=" * 60)
            self.logger.info("🚀 启动分布式数据收集和存储服务")
            self.logger.info("=" * 60)

            self.running = True

            # 设置核心组件
            self._setup_components()

            # 按顺序启动三个线程
            self.logger.info("启动数据处理线程...")
            self.data_processor.start()

            self.logger.info("启动目录监控线程...")
            self.directory_monitor.start()

            if self.config['cluster_nodes']:
                self.logger.info("启动网络订阅线程...")
                self.network_subscriber.start()
            else:
                self.logger.info("未配置集群节点，跳过网络订阅线程")

            self.logger.info("=" * 60)
            self.logger.info("✅ 存储服务启动完成！")
            self.logger.info("=" * 60)
            self.logger.info("💡 按 Ctrl+C 可安全退出服务")
            self.logger.info("=" * 60)

        except Exception as e:
            self.logger.error(f"启动存储服务失败: {e}")
            self.stop()
            raise

    def stop(self):
        """停止存储服务"""
        try:
            if not self.running:
                return

            self.logger.info("=" * 60)
            self.logger.info("🛑 停止分布式数据收集和存储服务")
            self.logger.info("=" * 60)

            self.running = False
            self.stop_event.set()

            # 按相反顺序停止三个线程
            if self.network_subscriber:
                self.logger.info("停止网络订阅线程...")
                self.network_subscriber.stop()

            if self.directory_monitor:
                self.logger.info("停止目录监控线程...")
                self.directory_monitor.stop()

            if self.data_processor:
                self.logger.info("停止数据处理线程...")
                self.data_processor.stop()

            # 清理资源
            if self.connection_pool:
                self.connection_pool.close_all()

            if self.zmq_context:
                self.zmq_context.term()

            self.logger.info("=" * 60)
            self.logger.info("✅ 存储服务已安全停止")
            self.logger.info("=" * 60)

        except Exception as e:
            self.logger.error(f"停止存储服务时出错: {e}")

    def health_check(self) -> bool:
        """健康检查"""
        try:
            # 检查服务运行状态
            if not self.running:
                return False

            # 检查数据库连接
            if self.connection_pool:
                with self.connection_pool.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    cursor.fetchone()

            # 检查消息队列
            if self.message_queue and self.message_queue.qsize() > self.config['queue_maxsize'] * 0.9:
                self.logger.warning("消息队列接近满载")
                return False

            return True

        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
            return False


def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('store.log', encoding='utf-8')
        ]
    )


def main():
    """主函数"""
    import signal
    import sys

    # 设置日志
    setup_logging()

    # 创建存储服务
    store_service = StoreService()

    # 设置信号处理器
    def signal_handler(signum, frame):
        print(f"\n🛑 收到信号 {signum}，正在安全关闭存储服务...")
        store_service.stop()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        # 启动服务
        store_service.start()

        # 保持运行状态
        while store_service.running:
            time.sleep(1)

            # 定期健康检查（每30秒）
            if int(time.time()) % 30 == 0:
                if not store_service.health_check():
                    store_service.logger.warning("⚠️ 健康检查失败")

    except KeyboardInterrupt:
        print("\n🛑 收到中断信号，正在关闭服务...")
    except Exception as e:
        print(f"❌ 服务运行失败: {e}")
        store_service.logger.error(f"服务运行失败: {e}")
    finally:
        store_service.stop()


if __name__ == "__main__":
    main()
