#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本 - 验证store.py模块的功能
测试文件稳定性检测、文件名解析、数据库操作和线程安全性

创建时间: 2025-01-25
"""

import json
import os
import sqlite3
import tempfile
import time
import unittest
from unittest.mock import patch, MagicMock
import threading
from queue import Queue

# 导入要测试的模块
from store import (
    FileStabilityChecker,
    FilenameParser,
    StoreConnectionPool,
    DataProcessorThread,
    StoreService
)


class TestFileStabilityChecker(unittest.TestCase):
    """测试文件稳定性检测器"""
    
    def setUp(self):
        self.checker = FileStabilityChecker(stability_time=0.01)  # 10ms for faster testing
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_stable_file(self):
        """测试稳定文件检测"""
        test_file = os.path.join(self.temp_dir, "stable_test.json")
        
        # 创建文件并写入内容
        with open(test_file, 'w') as f:
            f.write('{"test": "data"}')
        
        # 等待一段时间确保文件稳定
        time.sleep(0.02)
        
        # 检测文件稳定性
        self.assertTrue(self.checker.is_file_stable(test_file))
    
    def test_nonexistent_file(self):
        """测试不存在的文件"""
        nonexistent_file = os.path.join(self.temp_dir, "nonexistent.json")
        self.assertFalse(self.checker.is_file_stable(nonexistent_file))


class TestFilenameParser(unittest.TestCase):
    """测试文件名解析器"""
    
    def setUp(self):
        self.parser = FilenameParser()
    
    def test_original_data_filename(self):
        """测试原始数据文件名解析"""
        filename = "sat001_0_20250125120000.json"
        result = self.parser.parse_filename(filename)
        
        self.assertIsNotNone(result)
        self.assertEqual(result['id'], 'sat001')
        self.assertEqual(result['type'], '0')
        self.assertEqual(result['data_type'], 'original_data')
        self.assertEqual(result['timestamp'], '20250125120000')
    
    def test_feature_data_filename(self):
        """测试特征数据文件名解析"""
        filename = "sat002_1_20250125120001.json"
        result = self.parser.parse_filename(filename)
        
        self.assertIsNotNone(result)
        self.assertEqual(result['id'], 'sat002')
        self.assertEqual(result['type'], '1')
        self.assertEqual(result['data_type'], 'feature_data')
        self.assertEqual(result['timestamp'], '20250125120001')
    
    def test_invalid_filename(self):
        """测试无效文件名"""
        invalid_filenames = [
            "invalid.json",
            "sat001_0.json",
            "sat001_0_timestamp.txt",
            "not_a_valid_format.json"
        ]
        
        for filename in invalid_filenames:
            result = self.parser.parse_filename(filename)
            self.assertIsNone(result, f"应该无法解析文件名: {filename}")


class TestStoreConnectionPool(unittest.TestCase):
    """测试数据库连接池"""
    
    def setUp(self):
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.pool = StoreConnectionPool(self.temp_db.name, max_connections=3)
    
    def tearDown(self):
        self.pool.close_all()
        os.unlink(self.temp_db.name)
    
    def test_connection_pool_basic(self):
        """测试连接池基本功能"""
        with self.pool.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            self.assertEqual(result[0], 1)
    
    def test_multiple_connections(self):
        """测试多个连接"""
        connections = []
        
        # 获取多个连接
        for i in range(3):
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(f"SELECT {i}")
                result = cursor.fetchone()
                self.assertEqual(result[0], i)

class TestDataProcessorThread(unittest.TestCase):
    """测试数据处理线程"""
    
    def setUp(self):
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        self.connection_pool = StoreConnectionPool(self.temp_db.name, max_connections=3)
        self.message_queue = Queue()
        
        config = {
            'max_workers': 2
        }
        
        self.processor = DataProcessorThread(
            config=config,
            message_queue=self.message_queue,
            connection_pool=self.connection_pool
        )
    
    def tearDown(self):
        self.processor.stop()
        self.connection_pool.close_all()
        os.unlink(self.temp_db.name)
    
    def test_database_setup(self):
        """测试数据库表结构设置"""
        self.processor._setup_database_tables()
        
        # 检查表是否创建成功
        with self.connection_pool.get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查original_data表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='original_data'")
            self.assertIsNotNone(cursor.fetchone())
            
            # 检查feature_data表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='feature_data'")
            self.assertIsNotNone(cursor.fetchone())
    
    def test_process_original_data_message(self):
        """测试处理原始数据消息"""
        self.processor._setup_database_tables()
        
        # 准备测试数据
        test_data = {
            'sat_id': 'SAT001',
            'sat_category': 'TEST',
            'file_size': 1024.0,
            'timestamp': '20250125120000'
        }
        
        filename = "sat001_0_20250125120000.json"
        file_content = json.dumps(test_data).encode('utf-8')
        message = ('original_data', filename, file_content)
        
        # 处理消息
        self.processor._process_message(message)
        
        # 验证数据是否存储成功
        with self.connection_pool.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM original_data WHERE file_id = ?", ('sat001',))
            result = cursor.fetchone()
            self.assertIsNotNone(result)
            self.assertEqual(result['sat_id'], 'SAT001')
    
    def test_process_feature_data_message(self):
        """测试处理特征数据消息"""
        self.processor._setup_database_tables()
        
        # 准备测试数据
        test_data = {
            'sat_id': 'SAT002',
            'target_category': 'SHIP',
            'target_x': 100.5,
            'target_y': 200.5
        }
        
        filename = "sat002_1_20250125120001.json"
        file_content = json.dumps(test_data).encode('utf-8')
        message = ('feature_data', filename, file_content)
        
        # 处理消息
        self.processor._process_message(message)
        
        # 验证数据是否存储成功
        with self.connection_pool.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM feature_data WHERE file_id = ?", ('sat002',))
            result = cursor.fetchone()
            self.assertIsNotNone(result)
            self.assertEqual(result['sat_id'], 'SAT002')
            self.assertEqual(result['target_category'], 'SHIP')


def create_test_files():
    """创建测试文件"""
    test_dir = "./test_data"
    os.makedirs(test_dir, exist_ok=True)
    
    # 创建原始数据测试文件
    original_data = {
        'sat_id': 'SAT001',
        'sat_category': 'OPTICAL',
        'file_size': 2048.0,
        'image_resolution': 0.5,
        'timestamp': '20250125120000'
    }
    
    with open(os.path.join(test_dir, "sat001_0_20250125120000.json"), 'w') as f:
        json.dump(original_data, f)
    
    # 创建特征数据测试文件
    feature_data = {
        'sat_id': 'SAT002',
        'target_category': 'SHIP',
        'target_x': 150.0,
        'target_y': 250.0,
        'target_longitude': 120.5,
        'target_latitude': 30.2
    }
    
    with open(os.path.join(test_dir, "sat002_1_20250125120001.json"), 'w') as f:
        json.dump(feature_data, f)
    
    print(f"测试文件已创建在: {test_dir}")


def run_integration_test():
    """运行集成测试"""
    print("=" * 60)
    print("🧪 运行store.py集成测试")
    print("=" * 60)
    
    # 创建测试文件
    create_test_files()
    
    # 创建测试配置
    test_config = {
        'db_path': 'test_store.db',
        'max_connections': 5,
        'watch_directories': ['./test_data'],
        'zmq_publish_addr': 'tcp://127.0.0.1:13233',  # 使用不同端口避免冲突
        'cluster_nodes': [],  # 空列表，不启动网络订阅
        'subscribe_topics': ['original_data', 'feature_data'],
        'max_workers': 2,
        'queue_maxsize': 100,
        'service_enabled': True
    }
    
    # 创建存储服务实例
    store_service = StoreService()
    store_service.config = test_config
    
    try:
        print("启动存储服务...")
        store_service.start()
        
        print("等待文件处理...")
        time.sleep(3)
        
        print("执行健康检查...")
        health = store_service.health_check()
        print(f"健康检查结果: {'✅ 通过' if health else '❌ 失败'}")
        
        # 检查数据库中的数据
        with store_service.connection_pool.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM original_data")
            original_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM feature_data")
            feature_count = cursor.fetchone()[0]
            
            print(f"原始数据记录数: {original_count}")
            print(f"特征数据记录数: {feature_count}")
        
        print("✅ 集成测试完成")
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
    finally:
        print("停止存储服务...")
        store_service.stop()
        
        # 清理测试文件
        import shutil
        if os.path.exists('./test_data'):
            shutil.rmtree('./test_data')
        if os.path.exists('test_store.db'):
            os.remove('test_store.db')


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "integration":
        # 运行集成测试
        run_integration_test()
    else:
        # 运行单元测试
        unittest.main()
