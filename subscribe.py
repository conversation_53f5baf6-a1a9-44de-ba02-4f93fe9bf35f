#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订阅服务模块 - 订阅多个ZeroMQ主题，接收并持久化文件到本地
多源订阅，可靠存储

创建时间: 2025-01-25
"""

import configparser
import logging
import os
import signal
import sys
import threading
import time
import uuid
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, Any, List
from queue import Queue, Empty

import zmq


class MessageProcessor:
    """消息处理器 - 处理接收到的ZeroMQ消息"""
    
    def __init__(self, save_directory: str, max_workers: int = 5):
        self.save_directory = save_directory
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.processed_messages = set()  # 防止重复处理
        self.message_queue = Queue()
        self.running = False
        self.process_thread = None
        
        self.logger = logging.getLogger('MessageProcessor')
        
        # 确保保存目录存在
        os.makedirs(self.save_directory, exist_ok=True)
    
    def add_message(self, message_parts: List[bytes]):
        """添加消息到处理队列"""
        if not self.running:
            return
        
        try:
            message_id = str(uuid.uuid4())
            timestamp = time.time()
            
            self.message_queue.put((message_id, message_parts, timestamp))
            self.logger.debug(f"消息已添加到处理队列: {message_id}")
            
        except Exception as e:
            self.logger.error(f"添加消息到队列失败: {e}")
    
    def _process_message_queue(self):
        """处理消息队列"""
        while self.running:
            try:
                message_id, message_parts, timestamp = self.message_queue.get(timeout=1.0)
                
                # 提交到线程池处理
                self.executor.submit(
                    self._process_single_message,
                    message_id,
                    message_parts,
                    timestamp
                )
                
            except Empty:
                continue
            except Exception as e:
                self.logger.error(f"处理消息队列异常: {e}")
    
    def _process_single_message(self, message_id: str, message_parts: List[bytes], timestamp: float):
        """处理单个消息"""
        try:
            if len(message_parts) < 3:
                self.logger.warning(f"消息格式错误，至少需要3部分，实际收到{len(message_parts)}部分")
                return
            
            topic = message_parts[0].decode('utf-8')
            filename = message_parts[1].decode('utf-8')
            json_data = message_parts[2]
            # 根据消息部分数量判断消息类型
            if len(message_parts) == 3:
                # JSON消息格式: [topic, filename, json_data]
                self._process_file_message(topic, filename, json_data)
            else:
                self.logger.warning(f"未知的消息格式，部分数量: {len(message_parts)}")
            
        except Exception as e:
            self.logger.error(f"处理消息失败 {message_id}: {e}")
        
    def _process_file_message(self, topic: str, filename: str, json_data: bytes):
        """处理文件消息"""
        try:
            # 构建保存路径
            topic_dir = os.path.join(self.save_directory, 'files', topic)
            os.makedirs(topic_dir, exist_ok=True)
            
            # 生成唯一文件名（防止重名）
            file_path = os.path.join(topic_dir, filename)
            
            # 保存文件数据
            with open(file_path, 'wb') as f:
                f.write(json_data)
                        
            self.logger.info(f"文件消息已保存: {filename} (主题: {topic})")
            
        except Exception as e:
            self.logger.error(f"处理文件消息失败: {e}")
    
    def start(self):
        """启动消息处理器"""
        self.running = True
        
        # 启动消息处理线程
        self.process_thread = threading.Thread(target=self._process_message_queue, daemon=True)
        self.process_thread.start()
        
        self.logger.info("消息处理器已启动")
    
    def stop(self):
        """停止消息处理器"""
        self.running = False
        
        if self.process_thread and self.process_thread.is_alive():
            self.process_thread.join(timeout=5.0)
        
        self.executor.shutdown(wait=True)
        self.logger.info("消息处理器已停止")


class ZeroMQSubscriber:
    """ZeroMQ订阅器 - 订阅多个地址和主题"""
    
    def __init__(self, connect_addresses: List[str], topics: List[str], message_processor: MessageProcessor):
        self.connect_addresses = connect_addresses
        self.topics = topics
        self.message_processor = message_processor
        
        self.context = None
        self.subscriber = None
        self.running = False
        self.receive_thread = None
        self.received_count = 0
        
        self.logger = logging.getLogger('ZeroMQSubscriber')
    
    def start(self) -> bool:
        """启动ZeroMQ订阅器"""
        try:
            self.context = zmq.Context()
            self.subscriber = self.context.socket(zmq.SUB)
            
            # 设置socket选项
            self.subscriber.setsockopt(zmq.RCVHWM, 10000)  # 接收高水位标记
            self.subscriber.setsockopt(zmq.RCVBUF, 1024 * 1024)  # 接收缓冲区
            
            # 连接到所有地址
            for addr in self.connect_addresses:
                self.subscriber.connect(addr)
                self.logger.info(f"已连接到订阅地址: {addr}")
            
            # 订阅所有主题
            for topic in self.topics:
                self.subscriber.setsockopt_string(zmq.SUBSCRIBE, topic)
                self.logger.info(f"已订阅主题: {topic}")
            
            self.running = True
            
            # 启动接收线程
            self.receive_thread = threading.Thread(target=self._receive_messages, daemon=True)
            self.receive_thread.start()
            
            self.logger.info("ZeroMQ订阅器已启动")
            return True
            
        except Exception as e:
            self.logger.error(f"启动ZeroMQ订阅器失败: {e}")
            return False
    
    def _receive_messages(self):
        """接收ZeroMQ消息"""
        self.logger.info("开始接收订阅消息...")
        
        poller = zmq.Poller()
        poller.register(self.subscriber, zmq.POLLIN)
        
        while self.running:
            try:
                socks = dict(poller.poll(100))  # 100ms超时
                
                if self.subscriber in socks and socks[self.subscriber] == zmq.POLLIN:
                    # 接收多部分消息
                    message_parts = self.subscriber.recv_multipart()
                    
                    if message_parts:
                        self.received_count += 1
                        self.logger.debug(f"收到消息，部分数量: {len(message_parts)}")
                        
                        # 发送到消息处理器
                        self.message_processor.add_message(message_parts)
                
            except zmq.ZMQError as e:
                if self.running:
                    self.logger.error(f"ZeroMQ接收错误: {e}")
                break
            except Exception as e:
                self.logger.error(f"处理消息时发生错误: {e}")
                continue
        
        self.logger.info("停止接收订阅消息")
    
    def stop(self):
        """停止ZeroMQ订阅器"""
        self.running = False
        
        if self.receive_thread and self.receive_thread.is_alive():
            self.receive_thread.join(timeout=5.0)
        
        if self.subscriber:
            self.subscriber.close()
            self.subscriber = None
        
        if self.context:
            self.context.term()
            self.context = None
        
        self.logger.info("ZeroMQ订阅器已停止")
    
class SubscribeService:
    """订阅服务主类"""
    
    def __init__(self, config_path: str = "config.ini"):
        self.config_path = config_path
        self.config = self._load_config(config_path)
        
        # 设置日志
        self._setup_logging()
        
        self.logger = logging.getLogger('SubscribeService')
        
        # 初始化组件
        self.message_processor = None
        self.subscriber = None
        self.running = False
        
        # 加载配置
        self._load_service_config()
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        config = configparser.ConfigParser()
        config.read(config_path, encoding='utf-8')
        
        result = {}
        for section in config.sections():
            result[section] = dict(config[section])
        
        return result
    
    def _setup_logging(self):
        """设置日志配置"""
        log_config = self.config.get('logging', {})
        
        logging.basicConfig(
            level=getattr(logging, log_config.get('level', 'INFO')),
            format=log_config.get('format', '[%(asctime)s] [%(name)s] %(message)s'),
            datefmt=log_config.get('date_format', '%Y-%m-%d %H:%M:%S')
        )
    
    def _load_service_config(self):
        """加载服务配置"""
        sub_config = self.config.get('subscribe', {})
        
        # 保存目录
        self.save_directory = sub_config.get('save_directory', './data/save')
        
        # ZeroMQ连接地址
        connect_addrs_str = sub_config.get('zmq_connect_addrs', 'tcp://localhost:5555')
        self.connect_addresses = [addr.strip() for addr in connect_addrs_str.split(',')]
        
        # 订阅主题
        topics_str = sub_config.get('topics', 'file_updates,json_data')
        self.topics = [topic.strip() for topic in topics_str.split(',')]
        
        # 服务启用状态
        self.service_enabled = sub_config.get('subscribe_enable', 'true').lower() == 'true'
        
        self.logger.info(f"订阅服务配置:")
        self.logger.info(f"  保存目录: {self.save_directory}")
        self.logger.info(f"  连接地址: {self.connect_addresses}")
        self.logger.info(f"  订阅主题: {self.topics}")
        self.logger.info(f"  服务启用: {self.service_enabled}")
    
    def initialize(self):
        """初始化服务组件"""
        try:
            if not self.service_enabled:
                self.logger.info("订阅服务已禁用")
                return
            
            self.logger.info("初始化订阅服务...")
            
            # 初始化消息处理器
            self.message_processor = MessageProcessor(
                save_directory=self.save_directory,
                max_workers=5
            )
            
            # 初始化ZeroMQ订阅器
            self.subscriber = ZeroMQSubscriber(
                connect_addresses=self.connect_addresses,
                topics=self.topics,
                message_processor=self.message_processor
            )
            
            self.logger.info("订阅服务初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化订阅服务失败: {e}")
            raise
    
    def start(self):
        """启动服务"""
        try:
            if not self.service_enabled:
                self.logger.info("订阅服务已禁用，跳过启动")
                return
            
            self.running = True
            self.logger.info("启动订阅服务...")
            
            # 启动消息处理器
            self.message_processor.start()
            
            # 启动ZeroMQ订阅器
            if not self.subscriber.start():
                raise Exception("ZeroMQ订阅器启动失败")
            
            self.logger.info("订阅服务启动完成")
            
        except Exception as e:
            self.logger.error(f"启动订阅服务失败: {e}")
            raise
    
    def stop(self):
        """停止服务"""
        try:
            self.running = False
            self.logger.info("停止订阅服务...")
            
            # 停止ZeroMQ订阅器
            if self.subscriber:
                self.subscriber.stop()
            
            # 停止消息处理器
            if self.message_processor:
                self.message_processor.stop()
            
            self.logger.info("订阅服务已停止")
            
        except Exception as e:
            self.logger.error(f"停止订阅服务失败: {e}")

def main():
    """主函数 - 独立运行订阅服务"""
    service = SubscribeService()
    
    def signal_handler(signum, frame):
        print(f"\n收到信号 {signum}，正在关闭订阅服务...")
        service.stop()
        sys.exit(0)
    
    # 设置信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 初始化并启动服务
        service.initialize()
        service.start()
        
        print("订阅服务正在运行，按 Ctrl+C 退出...")
        print(f"保存目录: {service.save_directory}")
        print(f"连接地址: {service.connect_addresses}")
        print(f"订阅主题: {service.topics}")
        
        # 保持运行
        while service.running:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n收到中断信号，正在关闭服务...")
    except Exception as e:
        print(f"订阅服务运行失败: {e}")
        sys.exit(1)
    finally:
        service.stop()


if __name__ == "__main__":
    main()
