#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的store.py模块 - 验证与database.py一致的数据处理逻辑
测试数据库表结构、文件解析和数据存储功能

创建时间: 2025-01-25
"""

import json
import os
import sqlite3
import tempfile
import time
import unittest
from unittest.mock import patch, MagicMock
import threading
from queue import Queue

# 导入要测试的模块
from store import (
    StoreConnectionPool,
    DataProcessorThread,
    FilenameParser,
    StoreService
)


class TestUpdatedDataProcessor(unittest.TestCase):
    """测试更新后的数据处理器"""
    
    def setUp(self):
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        self.connection_pool = StoreConnectionPool(self.temp_db.name, max_connections=3)
        self.message_queue = Queue()
        
        config = {
            'max_workers': 2
        }
        
        self.processor = DataProcessorThread(
            config=config,
            message_queue=self.message_queue,
            connection_pool=self.connection_pool
        )
    
    def tearDown(self):
        self.processor.stop()
        self.connection_pool.close_all()
        os.unlink(self.temp_db.name)
    
    def test_database_tables_structure(self):
        """测试数据库表结构是否与database.py一致"""
        self.processor._setup_database_tables()
        
        with self.connection_pool.get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查origional_table表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='origional_table'")
            self.assertIsNotNone(cursor.fetchone(), "origional_table表应该存在")
            
            # 检查truth_table表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='truth_table'")
            self.assertIsNotNone(cursor.fetchone(), "truth_table表应该存在")
            
            # 检查feature_table表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='feature_table'")
            self.assertIsNotNone(cursor.fetchone(), "feature_table表应该存在")
            
            # 检查decision_table表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='decision_table'")
            self.assertIsNotNone(cursor.fetchone(), "decision_table表应该存在")
    
    def test_parse_filename(self):
        """测试文件名解析功能"""
        self.processor._setup_database_tables()
        
        # 测试有效的文件名
        valid_filename = "SAT001_0_20250125120000.json"
        result = self.processor._parse_filename(valid_filename)
        
        self.assertIsNotNone(result)
        self.assertEqual(result['sat_id'], 'SAT001')
        self.assertEqual(result['task_category'], '0')
        self.assertEqual(result['file_ext'], '.json')
        
        # 测试无效的文件名
        invalid_filename = "invalid.json"
        result = self.processor._parse_filename(invalid_filename)
        self.assertIsNone(result)
    
    def test_origin_data_storage(self):
        """测试原始数据存储功能"""
        self.processor._setup_database_tables()
        
        # 创建测试数据
        test_data = {
            "deviceInfo": {
                "deviceType": 0,
                "equipType": 1,
                "planedStartTime": 1640995200000,
                "planedEndTime": 1640995260000
            },
            "selectedImage": {
                "imageSize": 2048.0,
                "imageName": "test_image.jpg",
                "imagePath": "/test/path/image.jpg",
                "image_truth_width": 1024.0,
                "image_resolution": 0.5,
                "enuBaseLon": 120.0,
                "enuBaseLat": 30.0,
                "enuBaseAlt": 100.0,
                "satPosEnuX": 1000.0,
                "satPosEnuY": 2000.0,
                "satPosEnuZ": 3000.0,
                "camQENU_X": 0.1,
                "camQENU_Y": 0.2,
                "camQENU_Z": 0.3,
                "camQENU_W": 0.4,
                "satPosJ2000X": 5000.0,
                "satPosJ2000Y": 6000.0,
                "satPosJ2000Z": 7000.0,
                "satQbiX": 0.5,
                "satQbiY": 0.6,
                "satQbiZ": 0.7,
                "satQbiW": 0.8,
                "fovX": 45.0,
                "fovY": 45.0,
                "camPixelX": 512.0,
                "camPixelY": 512.0,
                "targetNum": 1,
                "targetInfo": [
                    {
                        "targetID": "TARGET001",
                        "targetFleetNumber": "FLEET_01",
                        "targetType": 1201,
                        "targetDirection": 90.0,
                        "targetPosLon": 121.0,
                        "targetPosLat": 31.0,
                        "targetPosAlt": 0.0,
                        "targetPosInSatX": 100.0,
                        "targetPosInSatY": 200.0,
                        "targetPosInSatZ": 300.0,
                        "returnbox": {
                            "x": 100.0,
                            "y": 150.0,
                            "width": 50.0,
                            "height": 30.0
                        }
                    }
                ]
            }
        }
        
        task_info = {
            'filename': 'SAT001_0_20250125120000.json',
            'sat_id': 'SAT001',
            'task_category': '0',
            'timestamp': 1640995200000,
            'file_ext': '.json'
        }
        
        content = json.dumps(test_data).encode('utf-8')
        
        # 存储数据
        self.processor._store_origin_data(content, task_info)
        
        # 验证origional_table数据
        with self.connection_pool.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM origional_table WHERE sat_id = ?", ('SAT001',))
            result = cursor.fetchone()
            self.assertIsNotNone(result)
            self.assertEqual(result['sat_category'], 'GEO')
            self.assertEqual(result['sat_load_category'], 'CCD')
            
            # 验证truth_table数据
            cursor.execute("SELECT * FROM truth_table WHERE sat_id = ?", ('SAT001',))
            truth_result = cursor.fetchone()
            self.assertIsNotNone(truth_result)
            self.assertEqual(truth_result['target_id'], 'TARGET001')
            self.assertEqual(truth_result['target_category'], 'CARRIER')
    
    def test_feature_data_storage(self):
        """测试特征数据存储功能"""
        self.processor._setup_database_tables()
        
        # 测试BHSF特征数据
        bhsf_data = [
            {
                "image_path": "/test/image1.jpg",
                "target_id": "TARGET001",
                "type": 1201,
                "truth": True,
                "position": {
                    "x1": 100,
                    "y1": 150,
                    "width": 50,
                    "height": 30
                }
            }
        ]
        
        task_info = {
            'filename': 'SAT001_BHSF_20250125120000.json',
            'sat_id': 'SAT001',
            'task_category': 'BHSF',
            'timestamp': 1640995200000,
            'file_ext': '.json'
        }
        
        content = json.dumps(bhsf_data).encode('utf-8')
        
        # 存储特征数据
        self.processor._store_feature_data_BHSF(content, task_info)
        
        # 验证feature_table数据
        with self.connection_pool.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM feature_table WHERE sat_id = ? AND target_id = ?", 
                          ('SAT001', 'TARGET001'))
            result = cursor.fetchone()
            self.assertIsNotNone(result)
            self.assertEqual(result['target_category'], 'CARRIER')
            self.assertEqual(result['target_truth'], True)
            self.assertEqual(result['target_x'], 100)
    
    def test_process_message_integration(self):
        """测试消息处理集成功能"""
        self.processor._setup_database_tables()
        
        # 创建测试消息
        test_data = {
            "deviceInfo": {"deviceType": 0, "equipType": 1},
            "selectedImage": {
                "imageSize": 1024.0,
                "imageName": "test.jpg",
                "targetNum": 0,
                "targetInfo": []
            }
        }
        
        filename = "SAT001_0_20250125120000.json"
        content = json.dumps(test_data).encode('utf-8')
        message = (filename, content)
        
        # 处理消息
        self.processor._process_message(message)
        
        # 验证数据是否存储
        with self.connection_pool.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM origional_table WHERE sat_id = ?", ('SAT001',))
            count = cursor.fetchone()[0]
            self.assertEqual(count, 1)


def create_test_config():
    """创建测试配置文件"""
    test_config = """
[store]
db_path = test_store.db
max_connections = 5
watch_directories = ./test_data
zmq_publish_addr = tcp://127.0.0.1:13234
cluster_nodes = 
subscribe_topics = original_data,feature_data
max_workers = 2
queue_maxsize = 100
service_enabled = true
"""
    
    with open('test_config.ini', 'w') as f:
        f.write(test_config)


def run_integration_test():
    """运行集成测试"""
    print("=" * 60)
    print("🧪 运行更新后的store.py集成测试")
    print("=" * 60)
    
    # 创建测试配置
    create_test_config()
    
    try:
        # 创建存储服务实例
        store_service = StoreService('test_config.ini')
        
        print("初始化存储服务...")
        store_service._setup_components()
        
        print("测试数据库表结构...")
        store_service.data_processor._setup_database_tables()
        
        # 检查表结构
        with store_service.connection_pool.get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查所有表是否存在
            tables = ['origional_table', 'truth_table', 'feature_table', 'decision_table']
            for table in tables:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                result = cursor.fetchone()
                if result:
                    print(f"✅ 表 {table} 创建成功")
                else:
                    print(f"❌ 表 {table} 创建失败")
        
        print("✅ 集成测试完成")
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理测试文件
        for file in ['test_config.ini', 'test_store.db']:
            if os.path.exists(file):
                os.remove(file)


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "integration":
        # 运行集成测试
        run_integration_test()
    else:
        # 运行单元测试
        unittest.main()
