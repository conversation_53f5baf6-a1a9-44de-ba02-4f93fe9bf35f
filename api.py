#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API服务模块 - 基于FastAPI提供RESTful接口，支持多客户端查询，结果通过ZeroMQ发布
高并发支持，异步处理，JSON序列化

创建时间: 2025-01-25
"""
import math
import asyncio
import configparser
import json
import logging
import signal
import sys
import time
import uuid
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Dict, Any, List, Optional

import uvicorn
import zmq
import zmq.asyncio
from fastapi import FastAPI, HTTPException, Query, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

from database import DatabaseManager


class ApiResponse(BaseModel):
    """API响应模型"""
    success: bool
    message: str
    data: Optional[Any] = None
    timestamp: str
    request_id: str

class AsyncZeroMQPublisher:
    """异步ZeroMQ发布器"""
    
    def __init__(self, bind_address: str, topics: List[str] = None):
        self.bind_address = bind_address
        self.topics = topics or ['api_responses']
        self.context = None
        self.publisher = None
        self.running = False
        self.publish_count = 0
        self.lock = asyncio.Lock()
        
        self.logger = logging.getLogger('AsyncZeroMQPublisher')
    
    async def start(self) -> bool:
        """启动异步ZeroMQ发布器"""
        try:
            self.context = zmq.asyncio.Context()
            self.publisher = self.context.socket(zmq.PUB)
            
            # 设置socket选项
            self.publisher.setsockopt(zmq.SNDHWM, 10000)
            self.publisher.setsockopt(zmq.LINGER, 1000)
            self.publisher.setsockopt(zmq.SNDBUF, 1024 * 1024)
            
            self.publisher.bind(self.bind_address)
            self.running = True
            
            self.logger.info(f"异步ZeroMQ发布器已启动: {self.bind_address}")
            return True
            
        except Exception as e:
            self.logger.error(f"启动异步ZeroMQ发布器失败: {e}")
            return False
    
    async def publish_json(self, filename: str, data: Any) -> bool:
        """异步发布JSON数据"""
        if not self.running or not self.publisher:
            self.logger.error("ZeroMQ发布器未运行")
            return False
        
        try:
            async with self.lock:
                # 选择单个主题
                topic = (
                    self.topics[0]
                    if isinstance(self.topics, list) and len(self.topics) > 0
                    else (self.topics if isinstance(self.topics, str) else 'api_responses')
                )

                # 序列化数据为bytes
                if isinstance(data, (dict, list)):
                    payload_str = json.dumps(data, ensure_ascii=False, separators=(',', ':'))
                    payload_bytes = payload_str.encode('utf-8')
                elif isinstance(data, str):
                    payload_bytes = data.encode('utf-8')
                elif isinstance(data, (bytes, bytearray)):
                    payload_bytes = bytes(data)
                else:
                    # 兜底序列化
                    payload_str = json.dumps(data, ensure_ascii=False, default=str)
                    payload_bytes = payload_str.encode('utf-8')

                # 构建消息：[主题, 文件名, 数据]
                message = [
                    topic.encode('utf-8'),
                    filename.encode('utf-8'),
                    payload_bytes
                ]

                # 发布消息（异步）
                await self.publisher.send_multipart(message)
                
                self.publish_count += 1
                
                return True
        except Exception as e:
            self.logger.error(f"api发布JSON数据失败: {e}")
            return False
    
    async def stop(self):
        """停止ZeroMQ发布器"""
        self.running = False
        
        if self.publisher:
            self.publisher.close()
            self.publisher = None
        
        if self.context:
            self.context.term()
            self.context = None
        
        self.logger.info("异步ZeroMQ发布器已停止")


class DatabaseService:
    """数据库服务封装"""
    
    def __init__(self, config_path: str = "config.ini"):
        self.db_manager = DatabaseManager(config_path)
        self.logger = logging.getLogger('DatabaseService')
       
    def quaternion_to_euler(w, x, y, z):
        """
        将四元数 (w, x, y, z) 转换为欧拉角 (yaw, pitch, roll)（单位：弧度）
        旋转顺序：Z-Y-X（先偏航 yaw，再俯仰 pitch，后滚转 roll）
        """
        # 计算偏航角 (yaw) - 绕Z轴旋转
        yaw = math.atan2(2 * (z * w + y * x), 1 - 2 * (z * z + y * y))
    
        # 计算俯仰角 (pitch) - 绕Y轴旋转
        sinp = 2 * (x * w + y * z)
        # 处理sinp超出[-1,1]的情况（浮点误差可能导致轻微越界）
        if abs(sinp) >= 1:
            pitch = math.copysign(math.pi / 2, sinp)  # 使用符号确定方向
        else:
            pitch = math.asin(sinp)
        
        # 计算滚转角 (roll) - 绕X轴旋转
        roll = math.atan2(2 * (w * y - x * z), 1 - 2 * (y * y + x * x))
        
        return yaw, pitch, roll  # 返回弧度值

    async def get_WZZH(self, device_id: str, timestamp: str):
        """获取位置转换任务"""
        try:
            def _get_position_tasks():
                with self.db_manager.connection_pool.get_connection() as conn:
                    cursor = conn.cursor()

                    # 1. 从origional_table获取卫星与载荷参数
                    origin_query = """
                    SELECT sat_j2000_x, sat_j2000_y, sat_j2000_z,
                        load_fov_x, load_fov_y, image_pixel_x, image_pixel_y, sat_qbi_x, sat_qbi_y, sat_qbi_z, sat_qbi_w
                    FROM origional_table 
                    WHERE sat_id = ? AND timestamp = ?
                    LIMIT 1
                    """

                    cursor.execute(origin_query, [device_id, timestamp])
                    origin_row = cursor.fetchone()

                    if not origin_row:
                        self.logger.warning(f"未找到device_id={device_id}, timestamp={timestamp}的源数据")
                        return {
                            "success": False,
                            "message": "未找到相关源数据",
                            "json_file_path": None
                        }

                    sat_j2000_x = origin_row[0] / 1000 or 0.0
                    sat_j2000_y = origin_row[1] / 1000 or 0.0  
                    sat_j2000_z = origin_row[2] / 1000 or 0.0


                    load_fov_x = origin_row[3] or 0.0
                    load_fov_y = origin_row[4] or 0.0
                    cam_pixel_x = origin_row[5] or 0.0
                    cam_pixel_y = origin_row[6] or 0.0
                    sat_qbi_x = origin_row[7] or 0.0
                    sat_qbi_y = origin_row[8] or 0.0
                    sat_qbi_z = origin_row[9] or 0.0
                    sat_qbi_w = origin_row[10] or 0.0

                    # 2. 在feature_table中根据sat_id与timestamp查询所有target_id
                    id_query = """
                    SELECT DISTINCT target_id,target_x,target_y
                    FROM feature_table
                    WHERE sat_id = ? AND timestamp = ?
                    """
                    cursor.execute(id_query, [device_id, timestamp])
                    id_rows = cursor.fetchall()
                    if not id_rows:
                        self.logger.warning(f"未在feature_table找到sat_id={device_id}, timestamp={timestamp}的目标ID")
                        return {
                            "success": False,
                            "message": "未找到相关目标数据"
                        }

                    # 3. 构建targets数组
                    targets = []
                    # 计算24小时计时的秒数(0-86399)，传入timestamp为unixTime（可能是毫秒或秒）
                    try:
                        ts_int = int(timestamp)
                        # 判断是否为毫秒级（> 10^12 大多为毫秒）
                        unix_seconds = ts_int // 1000 if ts_int > 10**12 else (ts_int if ts_int >= 0 else 0)
                        seconds_of_day = unix_seconds % 86400
                    except Exception:
                        seconds_of_day = 0
                        self.logger.warning(f"timestamp无法解析为unixTime整数，已将time置为0: {timestamp}")
                    for (target_id,target_x,target_y) in id_rows:

                        # 四元数转欧拉角（弧度转度）
                        yaw_r, pitch_r, roll_r = self.quaternion_to_euler(sat_qbi_w, sat_qbi_x, sat_qbi_y, sat_qbi_z)
                        yaw = math.degrees(yaw_r)
                        pitch = math.degrees(pitch_r)
                        roll = math.degrees(roll_r)

                        # 4. 按照输入文件定义格式组装数据
                        target_data = {
                            "sat_id": device_id,
                            "target_id": target_id,
                            "timestamp": str(timestamp),
                            "a": 6378.137,  # 地球椭圆模型长半轴（km）
                            "b": 6356.752314245,  # 地球椭圆模型短半轴（km）
                            "cx": float(cam_pixel_x),  # 图像主点x
                            "cy": float(cam_pixel_y),  # 图像主点y
                            "fx": float(load_fov_x),     # 载荷参数x
                            "fy": float(load_fov_y),     # 载荷参数y
                            "pixel_x": int(target_x),    # 特征目标x
                            "pixel_y": int(target_y),    # 特征目标y
                            "pitch": pitch,
                            "roll": roll,
                            "yaw": yaw,
                            "position": [
                                float(sat_j2000_x),
                                float(sat_j2000_y), 
                                float(sat_j2000_z)
                            ],
                            "time": seconds_of_day
                        }

                        targets.append(target_data)

                    # 5. 构建完整的JSON数据结构
                    json_data = {
                        "targets": targets
                    }
                    return json_data
            # 执行同步查询函数
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, _get_position_tasks)
            return result     
        except Exception as e:
            self.logger.error(f"获取位置转换任务失败: {e}")
            raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

    async def get_RHDW(self, device_id: str, timestamp: str):
        """获取融合定位任务"""
        try:
            def get_fusion_tasks():
                with self.db_manager.connection_pool.get_connection() as conn:
                    cursor = conn.cursor()

                    # 1. 在feature_table根据sat_id与timestamp获取相关的target_id
                    id_query = """
                    SELECT DISTINCT target_id
                    FROM feature_table 
                    WHERE sat_id = ? AND timestamp = ?
                    """

                    cursor.execute(id_query, [device_id, timestamp])
                    id_rows = cursor.fetchall()

                    if not id_rows:
                        self.logger.warning(f"未在feature_table找到sat_id={device_id}, timestamp={timestamp}的目标数据")
                        return {
                            "success": False,
                            "message": "未找到相关目标数据",
                            "json_file_path": None
                        }

                    # 2. 收集所有target_id
                    target_ids = [row[0] for row in id_rows]

                    # 3. 为每个target_id，从feature_table获取所有timestamp下的相关字段值（按timestamp排序）
                    observations = []

                    feature_query = """
                    SELECT target_category, target_longitude, target_latitude, timestamp
                    FROM feature_table 
                    WHERE sat_id = ? AND target_id = ?
                    ORDER BY timestamp ASC
                    """

                    for target_id in target_ids:
                        cursor.execute(feature_query, [device_id, target_id])
                        feature_rows = cursor.fetchall()

                        for feature_row in feature_rows:
                            target_category = feature_row[0]
                            target_longitude = feature_row[1] or 0.0
                            target_latitude = feature_row[2] or 0.0
                            feature_timestamp = feature_row[3]

                            # 将target_category转换为数值类型（默认1）
                            try:
                                target_type = int(target_category) if isinstance(target_category, (int, float)) or (isinstance(target_category, str) and target_category.isdigit()) else 1
                            except Exception:
                                target_type = 1

                            observation = {
                                "target_id": target_id,
                                "target_type": target_type,
                                "lon": float(target_longitude),
                                "lat": float(target_latitude),
                                "timestamp": int(feature_timestamp) if feature_timestamp else int(timestamp)
                            }

                            observations.append(observation)

                    # 4. 按照task.txt定义构建JSON（仅parameters和observations）
                    json_data = {
                        "parameters": {
                            "process_noise": 0.02,
                            "measurement_noise": 0.5,
                            "association_threshold": 0.02
                        },
                        "observations": observations
                    }

                    return json_data
            # 执行同步查询函数
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, get_fusion_tasks)
            return result     
        except Exception as e:
            self.logger.error(f"获取融合定位任务失败: {e}")
            raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

    async def get_YTFX(self, style: str):
        """获取意图分析任务"""
        try:
            def get_decision_tasks():
                with self.db_manager.connection_pool.get_connection() as conn:
                    cursor = conn.cursor()

                    # 1) 计算时间范围（毫秒）
                    try:
                        days = int(style)
                    except Exception:
                        days = 7
                    if days not in (3, 7):
                        days = 7
                    now_ms = int(datetime.now().timestamp() * 1000)
                    days_ms = days * 24 * 60 * 60 * 1000
                    operate_end = now_ms
                    # 若库中存在更大的时间戳，则将结束时间扩展到该时间点
                    # try:
                    #     cursor.execute("SELECT MAX(CAST(timestamp AS INTEGER)) FROM truth_table")
                    #     row_max = cursor.fetchone()
                    #     if row_max and row_max[0] is not None:
                    #         max_ts = int(row_max[0])
                    #         if max_ts > operate_end:
                    #             operate_end = max_ts
                    # except Exception:
                    #     pass
                    operate_begin = operate_end - days_ms

                    # 2) 分舰队顺序查询并组装结构
                    truth_query_by_fleet = """
                    SELECT target_id, target_category, target_longitude, target_latitude, timestamp
                    FROM truth_table
                    WHERE CAST(timestamp AS INTEGER) BETWEEN ? AND ? AND fleet_number = ?
                    ORDER BY CAST(timestamp AS INTEGER) ASC
                    """

                    def build_fleet_series(fleet_key: str):
                        ts_map: Dict[int, Dict[str, dict]] = {}
                        records = 0
                        cursor.execute(truth_query_by_fleet, [operate_begin, operate_end, fleet_key])
                        for target_id, target_category, lon, lat, ts in cursor.fetchall():
                            # 时间
                            try:
                                ts_i = int(ts)
                            except Exception:
                                continue

                            if target_category is not None:
                                str_map = { 'CARRIER': 0, 'BATTLESHIP': 2, 'DESTROYER': 1, 'SUPPLY': 3 }
                                ship_type = str_map.get(target_category, 0)
                            # 经纬度
                            try:
                                lon_v = float(lon)
                                lat_v = float(lat)
                            except Exception:
                                continue
                            if target_id is None or str(target_id).strip() == "":
                                continue
                            snapshot = ts_map.setdefault(ts_i, {})
                            snapshot[str(target_id)] = {
                                "ship_type": ship_type,
                                "latitude": lat_v,
                                "longitude": lon_v,
                                "timestamp": ts_i
                            }
                            records += 1
                        # 序列化为按时间排序的数组
                        series = [ts_map[t] for t in sorted(ts_map.keys())]
                        return series, records

                    fleet_01_series, rec_01 = build_fleet_series('FLEET_01')
                    fleet_02_series, rec_02 = build_fleet_series('FLEET_02')
                    fleet_03_series, rec_03 = build_fleet_series('FLEET_03')
                    total_records = rec_01 + rec_02 + rec_03

                    json_data = {
                        "query_days": str(days),
                        "analyse_time": str(operate_begin),
                        "FLEET_01": {"time_series_data": fleet_01_series},
                        "FLEET_02": {"time_series_data": fleet_02_series},
                        "FLEET_03": {"time_series_data": fleet_03_series},
                    }

                    return json_data
            # 执行同步查询函数
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, get_decision_tasks)
            return result   
        except Exception as e:
            self.logger.error(f"获取意图分析数据失败: {e}")
            raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")
    
    async def get_DZZN(self, device_id: str, timestamp: str):
        """获取电侦智能算法任务"""
        try:
            def _get_DZZN():
                with self.db_manager.connection_pool.get_connection() as conn:
                    cursor = conn.cursor()

                    id_query = """
                    SELECT image_path
                    FROM origional_table 
                    WHERE sat_id = ? AND timestamp = ?
                    LIMIT 1
                    """

                    cursor.execute(id_query, [device_id, timestamp])
                    paths_row = cursor.fetchone()

                    if not paths_row:
                        self.logger.warning(f"未找到device_id={device_id}, timestamp={timestamp}的源数据")
                        return {
                            "success": False,
                            "message": "未找到相关源数据",
                            "json_file_path": None
                        }

                    npy_path = paths_row.split(";")[0]
                    with open(npy_path, 'rb') as f:
                                file_data = f.read()
                    
                    return file_data
            # 执行同步查询函数
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, _get_DZZN)
            return result     
        except Exception as e:
            self.logger.error(f"获取位置转换任务失败: {e}")
            raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

    async def get_LDZN(self, device_id: str, timestamp: str):
        """获取雷达智能算法任务"""
        try:
            def _get_LDZN():
                with self.db_manager.connection_pool.get_connection() as conn:
                    cursor = conn.cursor()

                    id_query = """
                    SELECT image_path
                    FROM origional_table 
                    WHERE sat_id = ? AND timestamp = ?
                    LIMIT 1
                    """

                    cursor.execute(id_query, [device_id, timestamp])
                    paths_row = cursor.fetchone()

                    if not paths_row:
                        self.logger.warning(f"未找到device_id={device_id}, timestamp={timestamp}的源数据")
                        return {
                            "success": False,
                            "message": "未找到相关源数据",
                            "json_file_path": None
                        }

                    json_path = paths_row[0]
                    with open(json_path, 'rb') as f:
                                file_data = f.read()
                    
                    return file_data
            # 执行同步查询函数
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, _get_LDZN)
            return result     
        except Exception as e:
            self.logger.error(f"获取位置转换任务失败: {e}")
            raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

    async def get_origional_data(self, timestamps: Optional[str] = Query(None, description="时间戳")):
        """获取原始数据"""
        try:
            def get_origional():
                with self.db_manager.connection_pool.get_connection() as conn:
                    cursor = conn.cursor()

                    # 构建查询条件
                    conditions = []
                    params = []
                    
                    # 解析timestamps字符串为列表
                    parsed_timestamps = None
                    if timestamps:
                        try:
                            import ast
                            parsed_timestamps = ast.literal_eval(timestamps)
                            if not isinstance(parsed_timestamps, list):
                                parsed_timestamps = [parsed_timestamps]
                        except (ValueError, SyntaxError):
                            # 如果解析失败，尝试按逗号分割
                            parsed_timestamps = [t.strip() for t in timestamps.strip('[]').split(',')]
                    
                    if parsed_timestamps:
                        placeholders = ','.join(['?' for _ in parsed_timestamps])
                        conditions.append(f"timestamp IN ({placeholders})")
                        params.extend(parsed_timestamps)
                                            
                    where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""
                    
                    query = f"""
                    SELECT id, sat_id, sat_category, sat_load_category, planed_start_time, planed_end_time,
                        file_size, file_name, file_path, image_truth_width, image_resolution, timestamp,
                        enu_base_longitude, enu_base_latitude, enu_base_altitude, sat_enu_x, sat_enu_y, sat_enu_z,
                        cam_enu_x, cam_enu_y, cam_enu_z, cam_enu_w, sat_j2000_x, sat_j2000_y, sat_j2000_z,
                        sat_qbi_x, sat_qbi_y, sat_qbi_z, sat_qbi_w, load_fov_x, load_fov_y, image_pixel_x, image_pixel_y
                    FROM origional_table{where_clause}
                    ORDER BY timestamp ASC
                    """
                    
                    cursor.execute(query, params)
                    rows = cursor.fetchall()
                    
                    # 构建JSON数据
                    data_list = []
                    for row in rows:
                        data_item = {
                            "id": row[0],
                            "sat_id": row[1],
                            "sat_category": row[2],
                            "sat_load_category": row[3],
                            "planed_start_time": row[4],
                            "planed_end_time": row[5],
                            "file_size": row[6],
                            "file_name": row[7],
                            "file_path": row[8],
                            "image_truth_width": row[9],
                            "image_resolution": row[10],
                            "timestamp": row[11],
                            "enu_base_longitude": row[12],
                            "enu_base_latitude": row[13],
                            "enu_base_altitude": row[14],
                            "sat_enu_x": row[15],
                            "sat_enu_y": row[16],
                            "sat_enu_z": row[17],
                            "cam_enu_x": row[18],
                            "cam_enu_y": row[19],
                            "cam_enu_z": row[20],
                            "cam_enu_w": row[21],
                            "sat_j2000_x": row[22],
                            "sat_j2000_y": row[23],
                            "sat_j2000_z": row[24],
                            "sat_qbi_x": row[25],
                            "sat_qbi_y": row[26],
                            "sat_qbi_z": row[27],
                            "sat_qbi_w": row[28],
                            "load_fov_x": row[29],
                            "load_fov_y": row[30],
                            "image_pixel_x": row[31],
                            "image_pixel_y": row[32]
                        }
                        data_list.append(data_item)
                    
                    return data_list
            # 执行同步查询函数
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, get_origional)
            return result
                
        except Exception as e:
            self.logger.error(f"获取原始数据失败: {e}")
            raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")
        
    async def get_feature_data(self, timestamps: Optional[str] = Query(None, description="时间戳")):
        """获取特征数据"""
        try:
            def get_feature():
                with self.db_manager.connection_pool.get_connection() as conn:
                    cursor = conn.cursor()

                    # 解析timestamps字符串为列表
                    parsed_timestamps = None
                    if timestamps:
                        try:
                            import ast
                            parsed_timestamps = ast.literal_eval(timestamps)
                            if not isinstance(parsed_timestamps, list):
                                parsed_timestamps = [parsed_timestamps]
                        except (ValueError, SyntaxError):
                            parsed_timestamps = [t.strip() for t in timestamps.strip('[]').split(',')]
                    
                    # 构建查询条件
                    conditions = []
                    params = []

                    if parsed_timestamps:
                        placeholders = ','.join(['?' for _ in parsed_timestamps])
                        conditions.append(f"timestamp IN ({placeholders})")
                        params.extend(parsed_timestamps)
                    
                    where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""
                    
                    query = f"""
                    SELECT id, sat_id, fleet_number, target_category, target_id, timestamp, target_image_path,
                        target_truth, target_x, target_y, target_width, target_height, target_longitude, target_latitude,
                        target_fusion_longitude, target_fusion_latitude, target_longitude_speed, target_latitude_speed,
                        target_total_speed, target_npy_path
                    FROM feature_table{where_clause}
                    ORDER BY timestamp ASC
                    """
                    
                    cursor.execute(query, params)
                    rows = cursor.fetchall()
                    
                    # 构建JSON数据
                    data_list = []
                    for row in rows:
                        data_item = {
                            "id": row[0],
                            "sat_id": row[1],
                            "fleet_number": row[2],
                            "target_category": row[3],
                            "target_id": row[4],
                            "timestamp": row[5],
                            "target_image_path": row[6],
                            "target_truth": row[7],
                            "target_x": row[8],
                            "target_y": row[9],
                            "target_width": row[10],
                            "target_height": row[11],
                            "target_longitude": row[12],
                            "target_latitude": row[13],
                            "target_fusion_longitude": row[14],
                            "target_fusion_latitude": row[15],
                            "target_longitude_speed": row[16],
                            "target_latitude_speed": row[17],
                            "target_total_speed": row[18],
                            "target_npy_path": row[19]
                        }
                        data_list.append(data_item)
                    
                    return data_list
            # 执行同步查询函数
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, get_feature)
            return result
                
        except Exception as e:
            self.logger.error(f"获取特征数据失败: {e}")
            raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

    async def get_decision_data(self, timestamps: Optional[str] = Query(None, description="时间戳")):
        """获取决策数据"""
        try:
            def get_decision():
                with self.db_manager.connection_pool.get_connection() as conn:
                    cursor = conn.cursor()

                    # 解析timestamps字符串为列表
                    parsed_timestamps = None
                    if timestamps:
                        try:
                            import ast
                            parsed_timestamps = ast.literal_eval(timestamps)
                            if not isinstance(parsed_timestamps, list):
                                parsed_timestamps = [parsed_timestamps]
                        except (ValueError, SyntaxError):
                            parsed_timestamps = [t.strip() for t in timestamps.strip('[]').split(',')]
                    
                    # 构建查询条件
                    conditions = []
                    params = []

                    if parsed_timestamps:
                        placeholders = ','.join(['?' for _ in parsed_timestamps])
                        conditions.append(f"timestamp IN ({placeholders})")
                        params.extend(parsed_timestamps)
        
                    where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""
        
                    query = f"""
                    SELECT id, timestamp, query_days,fleet_number, speed_threat, heading_threat, distance_threat,
                        heading_angle, speed, lat, lon, intention
                    FROM decision_table{where_clause}
                    ORDER BY timestamp ASC
                    """
                    
                    cursor.execute(query, params)
                    rows = cursor.fetchall()
                    
                    # 构建JSON数据
                    data_list = []
                    for row in rows:
                        data_item = {
                            "id": row[0],
                            "timestamp": row[1],
                            "query_days": row[2],
                            "fleet_number": row[3],
                            "speed_threat": row[4],
                            "heading_threat": row[5],
                            "distance_threat": row[6],
                            "heading_angle": row[7],
                            "speed": row[8],
                            "lat": row[9],
                            "lon": row[10],
                            "intention": row[11]
                        }
                        data_list.append(data_item)
                                
                    return data_list
            # 执行同步查询函数
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, get_decision)
            return result
                
        except Exception as e:
            self.logger.error(f"获取决策数据失败: {e}")
            raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")
    
class APIService:
    """API服务主类"""
    
    def __init__(self, config_path: str = "config.ini"):
        self.config_path = config_path
        self.config = self._load_config(config_path)
        
        # 设置日志
        self._setup_logging()
        
        self.logger = logging.getLogger('APIService')
        
        # 初始化组件
        self.app = None
        self.publisher = None
        self.db_service = None
        self.running = False
        self.request_count = 0
        self.start_time = time.time()
        self._server = None  # 保存Uvicorn服务器实例，便于优雅关闭
        
        # 加载配置
        self._load_service_config()
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        config = configparser.ConfigParser()
        config.read(config_path, encoding='utf-8')
        
        result = {}
        for section in config.sections():
            result[section] = dict(config[section])
        
        return result
    
    def _setup_logging(self):
        """设置日志配置"""
        log_config = self.config.get('logging', {})
        
        logging.basicConfig(
            level=getattr(logging, log_config.get('level', 'INFO')),
            format=log_config.get('format', '[%(asctime)s] [%(name)s] %(message)s'),
            datefmt=log_config.get('date_format', '%Y-%m-%d %H:%M:%S')
        )
    
    def _load_service_config(self):
        """加载服务配置"""
        api_config = self.config.get('api', {})
        
        self.host = api_config.get('host', '0.0.0.0')
        self.port = int(api_config.get('port', 8000))
        self.json_zmq_bind_addr = api_config.get('json_zmq_bind_addr', 'tcp://0.0.0.0:5556')
        self.topics = [topic.strip() for topic in api_config.get('topics', 'api_responses').split(',')]
        self.service_enabled = api_config.get('api_enable', 'true').lower() == 'true'
        
        self.logger.info(f"API服务配置:")
        self.logger.info(f"  服务地址: {self.host}:{self.port}")
        self.logger.info(f"  ZeroMQ地址: {self.json_zmq_bind_addr}")
        self.logger.info(f"  发布主题: {self.topics}")
        self.logger.info(f"  服务启用: {self.service_enabled}")
    
    async def initialize(self):
        """初始化服务组件"""
        try:
            if not self.service_enabled:
                self.logger.info("API服务已禁用")
                return
            
            self.logger.info("初始化API服务...")
            
            # 初始化数据库服务
            self.db_service = DatabaseService(self.config_path)
            
            # 初始化ZeroMQ发布器
            self.publisher = AsyncZeroMQPublisher(
                bind_address=self.json_zmq_bind_addr,
                topics=self.topics
            )
            
            if not await self.publisher.start():
                raise Exception("ZeroMQ发布器启动失败")
            
            # 初始化FastAPI应用
            self._create_app()
            
            self.logger.info("API服务初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化API服务失败: {e}")
            raise
    
    def _create_app(self):
        """创建FastAPI应用"""
        self.app = FastAPI(
            title="分布式文件处理系统 API",
            description="基于FastAPI的高并发RESTful接口，支持多客户端查询",
            version="1.0.0",
            docs_url="/docs",
            redoc_url="/redoc"
        )
        
        # 添加CORS中间件
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"]
        )
        
        # 注册路由
        self._register_routes()
        
        # 注册异常处理器
        self._register_exception_handlers()
    
    def _register_routes(self):
        """注册API路由"""
        
        @self.app.get("/", response_model=ApiResponse)
        async def root():
            """API根路径"""
            self.request_count += 1
            
            response_data = {
                "service": "分布式文件处理系统 API",
                "version": "1.0.0",
                "uptime": int(time.time() - self.start_time),
                "request_count": self.request_count,
                "endpoints": [
                "/tasks/position/ - 位置转换查询接口",
                "/tasks/fusion/ - 融合定位查询接口", 
                "/tasks/analyse/ - 态势分析查询接口"
                ]
            }
            
            return ApiResponse(
                success=True,
                message="API服务正常运行",
                data=response_data,
                timestamp=datetime.now().isoformat(),
                request_id=str(uuid.uuid4())
            )
        
        @self.app.get("/tasks/position/", summary="位置转换查询")
        async def get_WZZH(
            device_id: str = Query(..., description="卫星ID"),
            timestamp: str = Query(..., description="时间戳"),
            background_tasks: BackgroundTasks = BackgroundTasks()
        ):
            try:
                filename = f"{device_id}_inputWZZH_{timestamp}.json"
                # 查询文件记录
                json_data = await self.db_service.get_WZZH(device_id, timestamp)
                
                # 异步发布查询结果
                if self.publisher:
                    background_tasks.add_task(
                        self.publisher.publish_json,
                        filename,
                        json_data
                    )
                print(json_data)
                return {
                    "success": True,
                    "message": f"位置转换数据查询成功",
                    "data": json_data
                }
                
            except Exception as e:
                self.logger.error(f"查询文件记录失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/tasks/fusion/", summary="融合定位查询")
        async def get_RHDW(
            device_id: str = Query(..., description="卫星ID"),
            timestamp: str = Query(..., description="时间戳"),
            background_tasks: BackgroundTasks = BackgroundTasks()
        ):
            try:
                filename = f"{device_id}_inputRHDW_{timestamp}.json"
                # 查询文件记录
                json_data = await self.db_service.get_RHDW(device_id, timestamp)
                
                # 异步发布查询结果
                if self.publisher:
                    background_tasks.add_task(
                        self.publisher.publish_json,
                        filename,
                        json_data
                    )
                
                return {
                    "success": True,
                    "message": f"融合定位数据查询成功",
                    "data": json_data
                }
                
            except Exception as e:
                self.logger.error(f"查询文件记录失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/tasks/intention/", summary="意图分析查询")
        async def get_YTFX(
            style: str = Query("7", description="样式，默认为7"),
            background_tasks: BackgroundTasks = BackgroundTasks()
        ):
            try:
                filename = f"inputYTFX_{style}.json"
                # 查询文件记录
                json_data = await self.db_service.get_YTFX(style)
                
                # 异步发布查询结果
                if self.publisher:
                    background_tasks.add_task(
                        self.publisher.publish_json,
                        filename,
                        json_data
                    )
                
                return {
                    "success": True,
                    "message": f"意图分析数据查询成功",
                    "data": json_data
                }
                
            except Exception as e:
                self.logger.error(f"查询文件记录失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/tasks/XDZN/", summary="电侦智能查询")
        async def get_DZZN(
            device_id: str = Query(..., description="卫星ID"),
            timestamp: str = Query(..., description="时间戳"),
            background_tasks: BackgroundTasks = BackgroundTasks()
        ):
            try:
                filename = f"{device_id}_inputXDZN_{timestamp}.json"
                # 查询文件记录
                json_data = await self.db_service.get_DZZN(device_id, timestamp)
                
                # 异步发布查询结果
                if self.publisher:
                    background_tasks.add_task(
                        self.publisher.publish_json,
                        filename,
                        json_data
                    )
                print(json_data)
                return {
                    "success": True,
                    "message": f"位置转换数据查询成功",
                    "data": json_data
                }
                
            except Exception as e:
                self.logger.error(f"查询文件记录失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/tasks/BKZN/", summary="雷达智能查询")
        async def get_LDZN(
            device_id: str = Query(..., description="卫星ID"),
            timestamp: str = Query(..., description="时间戳"),
            background_tasks: BackgroundTasks = BackgroundTasks()
        ):
            try:
                filename = f"{device_id}_inputLDZN_{timestamp}.json"
                # 查询文件记录
                json_data = await self.db_service.get_LDZN(device_id, timestamp)
                
                # 异步发布查询结果
                if self.publisher:
                    background_tasks.add_task(
                        self.publisher.publish_json,
                        filename,
                        json_data
                    )
                print(json_data)
                return {
                    "success": True,
                    "message": f"位置转换数据查询成功",
                    "data": json_data
                }
                
            except Exception as e:
                self.logger.error(f"查询文件记录失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/data/origional/", summary="原始数据查询")
        async def get_origional_data(
            timestamps: Optional[str] = Query(None, description="时间戳")
        ):
            try:
                # 查询文件记录
                json_data = await self.db_service.get_origional_data(timestamps)
                                
                return {
                    "success": True,
                    "message": f"源数据表查询成功，共{len(json_data)}条记录",
                    "data": json_data
                }
                
            except Exception as e:
                self.logger.error(f"查询文件记录失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/data/feature/", summary="特征数据查询")
        async def get_feature_data(
            timestamps: Optional[str] = Query(None, description="时间戳")
        ):
            try:
                # 查询文件记录
                json_data = await self.db_service.get_feature_data(timestamps)
                record_cnt = len(json_data)
                self.logger.info(f"特征数据表查询成功，共{record_cnt}条记录")
                return {
                    "success": True,
                    "message": f"特征数据表查询成功，共{record_cnt}条记录",
                    "data": json_data,
                    "record_cnt": record_cnt
                }
                
            except Exception as e:
                self.logger.error(f"查询文件记录失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/data/decision/", summary="决策数据查询")
        async def get_decision_data(
            timestamps: Optional[str] = Query(None, description="时间戳")
        ):
            try:
                # 查询文件记录
                json_data = await self.db_service.get_decision_data(timestamps)
                record_cnt = len(json_data)
                self.logger.info(f"决策数据表查询成功，共{record_cnt}条记录")
                return {
                    "success": True,
                    "message": f"决策数据表查询成功，共{record_cnt}条记录",
                    "data": json_data,
                    "record_cnt": record_cnt
                }
                
            except Exception as e:
                self.logger.error(f"查询文件记录失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
    
    def _register_exception_handlers(self):
        """注册异常处理器"""
        
        @self.app.exception_handler(HTTPException)
        async def http_exception_handler(request, exc):
            return JSONResponse(
                status_code=exc.status_code,
                content=ApiResponse(
                    success=False,
                    message=exc.detail,
                    data=None,
                    timestamp=datetime.now().isoformat(),
                    request_id=str(uuid.uuid4())
                ).model_dump()
            )
        
        @self.app.exception_handler(Exception)
        async def general_exception_handler(request, exc):
            self.logger.error(f"未处理的异常: {exc}")
            return JSONResponse(
                status_code=500,
                content=ApiResponse(
                    success=False,
                    message="内部服务器错误",
                    data=None,
                    timestamp=datetime.now().isoformat(),
                    request_id=str(uuid.uuid4())
                ).model_dump()
            )
    
    async def start(self):
        """启动API服务"""
        try:
            if not self.service_enabled:
                self.logger.info("API服务已禁用，跳过启动")
                return
            
            self.running = True
            self.logger.info(f"启动API服务: http://{self.host}:{self.port}")
            
            config = uvicorn.Config(
                app=self.app,
                host=self.host,
                port=self.port,
                log_level="info",
                access_log=True,
                workers=1
            )
            
            server = uvicorn.Server(config)
            # 避免与全局自定义信号处理器冲突（统一在 all.py 中处理信号）
            server.install_signal_handlers = False
            # 保存以便 stop() 时触发优雅退出
            self._server = server
            await server.serve()
            
        except Exception as e:
            self.logger.error(f"启动API服务失败: {e}")
            raise
    
    async def stop(self):
        """停止API服务"""
        try:
            self.running = False
            self.logger.info("停止API服务...")
            
            # 通知Uvicorn优雅退出
            if self._server is not None:
                try:
                    self._server.should_exit = True
                except Exception as _:
                    pass
            
            # 停止ZeroMQ发布器
            if self.publisher:
                await self.publisher.stop()
            
            # 关闭数据库连接
            if self.db_service and self.db_service.db_manager:
                self.db_service.db_manager.close()
            
            self.logger.info("API服务已停止")
            
        except Exception as e:
            self.logger.error(f"停止API服务失败: {e}")


async def main():
    """主函数 - 独立运行API服务"""
    service = APIService()
    
    def signal_handler(signum, frame):
        print(f"\n收到信号 {signum}，正在关闭API服务...")
        asyncio.create_task(service.stop())
        sys.exit(0)
    
    # 设置信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 初始化并启动服务
        await service.initialize()
        
        print("API服务正在启动...")
        print(f"服务地址: http://{service.host}:{service.port}")
        print(f"API文档: http://{service.host}:{service.port}/docs")
        print("按 Ctrl+C 退出...")
        
        await service.start()
        
    except KeyboardInterrupt:
        print("\n收到中断信号，正在关闭服务...")
    except Exception as e:
        print(f"API服务运行失败: {e}")
        sys.exit(1)
    finally:
        await service.stop()


if __name__ == "__main__":
    asyncio.run(main())
