# 分布式文件处理系统

基于Python 3.10.18的高性能分布式文件处理系统，包含四个核心模块，使用Watchdog、ZeroMQ和SQLite技术。

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   文件监控目录   │    │   ZeroMQ消息队列 │    │   文件保存目录   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       │                       ▼
┌─────────────────┐              │              ┌─────────────────┐
│  database.py    │              │              │  subscribe.py   │
│  数据库模块      │              │              │  订阅服务模块    │
└─────────────────┘              │              └─────────────────┘
         │                       │                       ▲
         ▼                       │                       │
┌─────────────────┐              │              ┌─────────────────┐
│   SQLite DB     │              ▼              │  file_publish.py│
│   结构化存储     │    ┌─────────────────┐      │  文件发布模块    │
└─────────────────┘    │     api.py      │      └─────────────────┘
                       │   API服务模块    │             ▲
                       └─────────────────┘             │
                              │                        │
                              ▼                        │
                       ┌─────────────────┐    ┌─────────────────┐
                       │  RESTful API    │    │   文件系统事件   │
                       │  HTTP接口       │    │   Watchdog监控   │
                       └─────────────────┘    └─────────────────┘
```

## 📦 核心模块

### 1. 数据库模块 (`database.py`)
- **功能**: 监听指定目录，解析不同格式文件内容，结构化存储到SQLite数据库
- **特性**: 支持并发写入，文件类型可扩展，连接池管理
- **支持格式**: JSON

### 2. 文件发布模块 (`file_publish.py`)
- **功能**: 监控文件系统事件，通过ZeroMQ发布新文件到指定端点
- **特性**: 使用Watchdog监控文件系统，高效事件处理，防重复发布

### 3. API服务模块 (`api.py`)
- **功能**: 基于FastAPI提供RESTful接口，支持多客户端查询，结果通过ZeroMQ发布
- **特性**: 高并发支持，异步处理，JSON序列化，自动API文档

### 4. 订阅服务模块 (`subscribe.py`)
- **功能**: 订阅多个ZeroMQ主题，接收并持久化文件到本地
- **特性**: 多源订阅，可靠存储，消息去重

### 5. 统一启动模块 (`all.py`)
- **功能**: 直接启动四个服务模块，支持Ctrl+C安全退出
- **特性**: 统一管理，安全释放资源，健康检查

## 🚀 快速开始

### 环境要求
- Python 3.10.18+
- Windows/Linux/macOS

### 安装依赖
```bash
pip install -r requirements.txt
```

### 配置系统
编辑 `config.ini` 文件，配置各模块参数：

```ini
# 数据库模块配置
[database]
path = database.db
watch_directory = ./data/watch
database_enable = true

# 文件发布模块配置
[file_publish]
watch_paths = ./data/watch;./data/image
zmq_publish_addr = tcp://*:5555
file_types = *.json,*.csv,*.txt
file_publish_enable = true

# API服务模块配置
[api]
host = 0.0.0.0
port = 8000
json_zmq_bind_addr = tcp://0.0.0.0:5556
topics = api_responses
api_enable = true

# 订阅服务模块配置
[subscribe]
save_directory = ./data/save
zmq_connect_addrs = tcp://localhost:5555,tcp://otherhost:5557
topics = file_updates,json_data
subscribe_enable = true
```

### 启动系统

#### 方式一：统一启动（推荐）
```bash
python all.py
```

#### 方式二：单独启动各模块
```bash
# 启动数据库服务
python database.py

# 启动文件发布服务
python file_publish.py

# 启动API服务
python api.py

# 启动订阅服务
python subscribe.py
```

## 📖 使用指南

### 1. 文件处理流程

1. **放置文件**: 将JSON/CSV/TXT文件放入 `./data/watch` 目录
2. **自动处理**: 系统自动检测、解析并存储到数据库
3. **消息发布**: 通过ZeroMQ发布文件更新消息
4. **API查询**: 通过RESTful API查询处理结果

### 2. API接口使用

启动系统后，访问API文档：
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

#### 主要接口：

**获取系统状态**
```bash
curl http://localhost:8000/
```

**查询文件记录**
```bash
curl -X POST http://localhost:8000/query/files \
  -H "Content-Type: application/json" \
  -d '{"file_type": ".json", "limit": 10}'
```

**获取文件列表**
```bash
curl "http://localhost:8000/files?file_type=.json&limit=10"
```

**自定义查询**
```bash
curl -X POST "http://localhost:8000/query/custom?query=SELECT * FROM file_records LIMIT 5"
```

### 3. 监控和日志

系统提供详细的日志输出，包括：
- 文件处理状态
- ZeroMQ消息传递
- API请求响应
- 错误和异常信息

## 🔧 配置详解

### 数据库配置
```ini
[database]
path = database.db                    # 数据库文件路径
watch_directory = ./data/watch        # 监控目录
database_enable = true                # 是否启用数据库服务
max_connections = 10                  # 最大连接数
connection_timeout = 30               # 连接超时时间
```

### 文件发布配置
```ini
[file_publish]
watch_paths = ./data/watch;./data/image    # 监控路径（分号分隔）
zmq_publish_addr = tcp://*:5555            # ZeroMQ发布地址
file_types = *.json,*.csv,*.txt            # 支持的文件类型
file_publish_enable = true                 # 是否启用发布服务
```

### API服务配置
```ini
[api]
host = 0.0.0.0                        # 服务主机地址
port = 8000                           # 服务端口
json_zmq_bind_addr = tcp://0.0.0.0:5556   # ZeroMQ绑定地址
topics = api_responses                # 发布主题
api_enable = true                     # 是否启用API服务
```

### 订阅服务配置
```ini
[subscribe]
save_directory = ./data/save          # 文件保存目录
zmq_connect_addrs = tcp://localhost:5555,tcp://otherhost:5557  # 连接地址
topics = file_updates,json_data       # 订阅主题
subscribe_enable = true               # 是否启用订阅服务
```

## 🗂️ 目录结构

```
分布式文件处理系统/
├── config.ini              # 配置文件
├── requirements.txt         # 依赖包列表
├── README.md               # 说明文档
├── all.py                  # 统一启动模块
├── database.py             # 数据库模块
├── file_publish.py         # 文件发布模块
├── api.py                  # API服务模块
├── subscribe.py            # 订阅服务模块
├── database.db             # SQLite数据库文件（自动创建）
└── data/                   # 数据目录
    ├── watch/              # 文件监控目录
    ├── image/              # 图片文件目录
    └── save/               # 订阅文件保存目录
        ├── json/           # JSON消息保存
        └── files/          # 文件消息保存
```

## 🔍 数据库结构

系统自动创建以下数据表：

### file_records (文件记录表)
- `id`: 主键
- `filename`: 文件名
- `file_path`: 文件路径
- `file_type`: 文件类型
- `file_size`: 文件大小
- `created_time`: 创建时间
- `processed_time`: 处理时间
- `status`: 处理状态
- `checksum`: 文件校验和

### json_data (JSON数据表)
- `id`: 主键
- `file_record_id`: 关联文件记录ID
- `json_key`: JSON键
- `json_value`: JSON值
- `data_type`: 数据类型
- `created_time`: 创建时间

### csv_data (CSV数据表)
- `id`: 主键
- `file_record_id`: 关联文件记录ID
- `row_number`: 行号
- `column_name`: 列名
- `column_value`: 列值
- `created_time`: 创建时间

### text_data (文本数据表)
- `id`: 主键
- `file_record_id`: 关联文件记录ID
- `content`: 文本内容
- `line_count`: 行数
- `word_count`: 词数
- `char_count`: 字符数
- `created_time`: 创建时间

## 🚨 故障排除

### 常见问题

**1. 端口被占用**
```
错误: [Errno 10048] Only one usage of each socket address is normally permitted
解决: 修改config.ini中的端口配置，或停止占用端口的程序
```

**2. 文件权限问题**
```
错误: PermissionError: [Errno 13] Permission denied
解决: 确保Python进程有读写目录的权限
```

**3. ZeroMQ连接失败**
```
错误: zmq.error.ZMQError: Address already in use
解决: 检查ZeroMQ地址配置，确保没有重复绑定
```

**4. 数据库锁定**
```
错误: sqlite3.OperationalError: database is locked
解决: 确保没有其他程序占用数据库文件
```

### 日志级别调整

在config.ini中修改日志级别：
```ini
[logging]
level = DEBUG  # DEBUG, INFO, WARNING, ERROR, CRITICAL
```

## 🔒 安全建议

1. **网络安全**: 在生产环境中，限制API服务的访问IP
2. **文件安全**: 定期清理临时文件和日志文件
3. **权限控制**: 使用最小权限原则运行服务
4. **数据备份**: 定期备份SQLite数据库文件

## 📈 性能优化

1. **数据库优化**: 调整连接池大小和SQLite参数
2. **并发控制**: 根据硬件配置调整线程池大小
3. **内存管理**: 监控内存使用，避免内存泄漏
4. **网络优化**: 调整ZeroMQ缓冲区大小

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看LICENSE文件了解详情

## 📞 支持

如有问题或建议，请：
1. 查看本文档的故障排除部分
2. 检查系统日志输出
3. 提交Issue或联系开发团队

---

**版本**: 1.0.0  
**更新时间**: 2025-01-25  
**Python版本**: 3.10.18+
