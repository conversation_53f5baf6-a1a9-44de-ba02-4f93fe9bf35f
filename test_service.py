#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试service.py模块的功能
验证配置检查、数据库管理和服务启动功能

创建时间: 2025-01-25
"""

import os
import tempfile
import unittest
from unittest.mock import patch, MagicMock
import configparser
import sqlite3

# 导入要测试的模块
from service import ConfigValidator, DatabaseManager


class TestConfigValidator(unittest.TestCase):
    """测试配置验证器"""
    
    def setUp(self):
        self.temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.ini', delete=False)
        self.config_path = self.temp_config.name
    
    def tearDown(self):
        self.temp_config.close()
        if os.path.exists(self.config_path):
            os.unlink(self.config_path)
    
    def create_valid_config(self):
        """创建有效的配置文件"""
        config_content = """
[store]
db_path = test_store.db
max_connections = 10
watch_directories = ./test_data1;./test_data2
zmq_publish_addr = tcp://0.0.0.0:13232
cluster_nodes = 
subscribe_topics = original_data,feature_data
max_workers = 4
queue_maxsize = 1000
service_enabled = true

[search]
host = 0.0.0.0
port = 8001
save_directory = ./search_results
file_prefix = search
search_enable = true

[database]
db_path = test_store.db
max_connections = 20
"""
        with open(self.config_path, 'w') as f:
            f.write(config_content)
    
    def test_valid_config(self):
        """测试有效配置"""
        self.create_valid_config()
        
        validator = ConfigValidator(self.config_path)
        result = validator.validate()
        
        self.assertTrue(result)
        self.assertEqual(len(validator.validation_errors), 0)
    
    def test_missing_config_file(self):
        """测试配置文件不存在"""
        os.unlink(self.config_path)
        
        validator = ConfigValidator(self.config_path)
        result = validator.validate()
        
        self.assertFalse(result)
        self.assertGreater(len(validator.validation_errors), 0)
    
    def test_missing_store_section(self):
        """测试缺少store配置节"""
        config_content = """
[search]
host = 0.0.0.0
port = 8001
save_directory = ./search_results
file_prefix = search
"""
        with open(self.config_path, 'w') as f:
            f.write(config_content)
        
        validator = ConfigValidator(self.config_path)
        result = validator.validate()
        
        self.assertFalse(result)
        self.assertTrue(any("缺少 [store] 配置节" in error for error in validator.validation_errors))
    
    def test_invalid_port_config(self):
        """测试无效端口配置"""
        config_content = """
[store]
db_path = test.db
max_connections = 10
watch_directories = ./test
zmq_publish_addr = tcp://0.0.0.0:13232
max_workers = 4
queue_maxsize = 1000

[search]
host = 0.0.0.0
port = 99999
save_directory = ./search_results
file_prefix = search
"""
        with open(self.config_path, 'w') as f:
            f.write(config_content)
        
        validator = ConfigValidator(self.config_path)
        result = validator.validate()
        
        self.assertFalse(result)
        self.assertTrue(any("port 必须在 1024-65535 范围内" in error for error in validator.validation_errors))
    
    def test_port_conflict(self):
        """测试端口冲突"""
        config_content = """
[store]
db_path = test.db
max_connections = 10
watch_directories = ./test
zmq_publish_addr = tcp://0.0.0.0:8001
max_workers = 4
queue_maxsize = 1000

[search]
host = 0.0.0.0
port = 8001
save_directory = ./search_results
file_prefix = search

[api]
port = 8001
"""
        with open(self.config_path, 'w') as f:
            f.write(config_content)
        
        validator = ConfigValidator(self.config_path)
        result = validator.validate()
        
        self.assertFalse(result)
        self.assertTrue(any("端口冲突" in error for error in validator.validation_errors))


class TestDatabaseManager(unittest.TestCase):
    """测试数据库管理器"""
    
    def setUp(self):
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db_path = self.temp_db.name
        
        self.db_manager = DatabaseManager(self.db_path)
    
    def tearDown(self):
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
    
    def test_ensure_wal_mode(self):
        """测试WAL模式设置"""
        result = self.db_manager.ensure_wal_mode()
        self.assertTrue(result)
        
        # 验证WAL模式是否设置成功
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("PRAGMA journal_mode")
        mode = cursor.fetchone()[0]
        conn.close()
        
        self.assertEqual(mode.upper(), 'WAL')
    
    def test_concurrent_access(self):
        """测试并发访问"""
        # 先设置WAL模式
        self.db_manager.ensure_wal_mode()
        
        # 测试并发访问
        result = self.db_manager.test_concurrent_access()
        self.assertTrue(result)
    
    def test_nonexistent_database_directory(self):
        """测试不存在的数据库目录"""
        nonexistent_path = "/nonexistent/directory/test.db"
        db_manager = DatabaseManager(nonexistent_path)
        
        # 应该能够处理不存在的目录
        result = db_manager.ensure_wal_mode()
        # 在某些系统上可能失败，这是正常的
        # 主要是测试不会崩溃


def create_test_config_file():
    """创建测试配置文件"""
    config_content = """
[store]
db_path = test_service.db
max_connections = 5
watch_directories = ./test_data
zmq_publish_addr = tcp://127.0.0.1:13234
cluster_nodes = 
subscribe_topics = original_data,feature_data
max_workers = 2
queue_maxsize = 100
service_enabled = false

[search]
host = 127.0.0.1
port = 8002
save_directory = ./test_search_results
file_prefix = test_search
search_enable = false

[database]
db_path = test_service.db
max_connections = 10
"""
    
    with open('test_service_config.ini', 'w') as f:
        f.write(config_content)


def test_config_validation():
    """测试配置验证功能"""
    print("=" * 60)
    print("🧪 测试配置验证功能")
    print("=" * 60)
    
    # 创建测试配置文件
    create_test_config_file()
    
    try:
        # 测试配置验证
        validator = ConfigValidator('test_service_config.ini')
        result = validator.validate()
        
        if result:
            print("✅ 配置验证测试通过")
        else:
            print("❌ 配置验证测试失败")
            for error in validator.validation_errors:
                print(f"  错误: {error}")
            for warning in validator.validation_warnings:
                print(f"  警告: {warning}")
        
        return result
        
    except Exception as e:
        print(f"❌ 配置验证测试异常: {e}")
        return False
    finally:
        # 清理测试文件
        if os.path.exists('test_service_config.ini'):
            os.remove('test_service_config.ini')


def test_database_management():
    """测试数据库管理功能"""
    print("\n" + "=" * 60)
    print("🧪 测试数据库管理功能")
    print("=" * 60)
    
    db_path = 'test_service.db'
    
    try:
        # 测试数据库管理
        db_manager = DatabaseManager(db_path)
        
        # 测试WAL模式设置
        wal_result = db_manager.ensure_wal_mode()
        if wal_result:
            print("✅ WAL模式设置测试通过")
        else:
            print("❌ WAL模式设置测试失败")
        
        # 测试并发访问
        concurrent_result = db_manager.test_concurrent_access()
        if concurrent_result:
            print("✅ 并发访问测试通过")
        else:
            print("❌ 并发访问测试失败")
        
        return wal_result and concurrent_result
        
    except Exception as e:
        print(f"❌ 数据库管理测试异常: {e}")
        return False
    finally:
        # 清理测试文件
        for file in [db_path, f"{db_path}-wal", f"{db_path}-shm"]:
            if os.path.exists(file):
                os.remove(file)


def run_integration_test():
    """运行集成测试"""
    print("\n" + "=" * 60)
    print("🧪 运行service.py集成测试")
    print("=" * 60)
    
    # 测试配置验证
    config_ok = test_config_validation()
    
    # 测试数据库管理
    db_ok = test_database_management()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    if config_ok and db_ok:
        print("🎉 所有测试通过！service.py功能正常")
        print("✅ 配置验证功能正常")
        print("✅ 数据库管理功能正常")
        print("✅ 可以安全使用服务管理器")
    else:
        print("❌ 部分测试失败")
        print(f"配置验证: {'✅' if config_ok else '❌'}")
        print(f"数据库管理: {'✅' if db_ok else '❌'}")
    
    return config_ok and db_ok


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "integration":
        # 运行集成测试
        success = run_integration_test()
        sys.exit(0 if success else 1)
    else:
        # 运行单元测试
        unittest.main()
