2025-09-01 16:16:34,856 - ServiceManager - INFO - ================================================================================
2025-09-01 16:16:34,857 - ServiceManager - INFO - 🚀 启动分布式文件处理系统服务
2025-09-01 16:16:34,858 - ServiceManager - INFO - ================================================================================
2025-09-01 16:16:34,859 - ServiceManager - INFO - 📋 步骤1: 验证配置文件
2025-09-01 16:16:34,859 - ConfigValidator - INFO - 开始验证配置文件: config.ini
2025-09-01 16:16:34,863 - ConfigValidator - WARNING - 配置验证发现警告:
2025-09-01 16:16:34,864 - ConfigValidator - WARNING -   ⚠️ 数据库路径不一致: store.db_path='store.db', database.db_path='data.db'
2025-09-01 16:16:34,865 - ConfigValidator - WARNING -   ⚠️ 监控目录不存在: /home/<USER>/SourceData
2025-09-01 16:16:34,867 - ConfigValidator - WARNING -   ⚠️ 监控目录不存在: /home/<USER>/dataShare
2025-09-01 16:16:34,868 - ServiceManager - INFO - ✅ 配置验证通过
2025-09-01 16:16:34,868 - ServiceManager - INFO - 🗄️ 步骤2: 准备数据库
2025-09-01 16:16:34,870 - DatabaseManager - INFO - 检查数据库WAL模式: store.db
2025-09-01 16:16:34,872 - DatabaseManager - INFO - 数据库文件不存在，将在首次连接时创建
2025-09-01 16:16:34,881 - DatabaseManager - INFO - 当前journal模式: delete，正在切换到WAL模式
2025-09-01 16:16:34,888 - DatabaseManager - INFO - WAL模式设置成功: wal
2025-09-01 16:16:34,889 - DatabaseManager - INFO - 数据库并发优化参数设置完成
2025-09-01 16:16:34,889 - DatabaseManager - INFO - 测试数据库并发访问...
2025-09-01 16:16:34,902 - DatabaseManager - INFO - ✅ 数据库并发访问测试通过
2025-09-01 16:16:34,905 - ServiceManager - INFO - ✅ 数据库准备完成
2025-09-01 16:16:34,906 - ServiceManager - INFO - 📊 步骤3: 配置摘要
2025-09-01 16:16:34,906 - ServiceManager - INFO -   📡 Store服务:
2025-09-01 16:16:34,907 - ServiceManager - INFO -     - ZeroMQ地址: tcp://0.0.0.0:13232
2025-09-01 16:16:34,907 - ServiceManager - INFO -     - 监控目录: 2 个
2025-09-01 16:16:34,908 - ServiceManager - INFO -     - 工作线程: 4
2025-09-01 16:16:34,908 - ServiceManager - INFO -     - 数据库连接池: 10
2025-09-01 16:16:34,908 - ServiceManager - INFO -   🔍 Search服务:
2025-09-01 16:16:34,909 - ServiceManager - INFO -     - HTTP地址: http://0.0.0.0:8001
2025-09-01 16:16:34,909 - ServiceManager - INFO -     - API文档: http://0.0.0.0:8001/docs
2025-09-01 16:16:34,910 - ServiceManager - INFO -     - 文件保存: ./search_results
2025-09-01 16:16:34,910 - ServiceManager - INFO -   🗄️ 数据库:
2025-09-01 16:16:34,910 - ServiceManager - INFO -     - 路径: store.db
2025-09-01 16:16:34,911 - ServiceManager - INFO -     - 模式: WAL (支持并发)
2025-09-01 16:16:34,911 - ServiceManager - INFO - 🎯 步骤4: 启动服务
2025-09-01 16:16:34,912 - ServiceManager - INFO -   📡 启动Store服务...
2025-09-01 16:16:36,916 - ServiceManager - INFO -   ✅ Store服务启动成功
2025-09-01 16:16:36,918 - ServiceManager - INFO - ⏳ 等待Store服务完全启动...
2025-09-01 16:16:39,923 - ServiceManager - INFO -   🔍 启动Search服务...
2025-09-01 16:16:41,928 - ServiceManager - ERROR -   ❌ Search服务启动失败:
2025-09-01 16:16:41,929 - ServiceManager - ERROR -     错误: Traceback (most recent call last):
  File "/mnt/hgfs/ShareData/502/dataShare/search.py", line 11, in <module>
    import aiofiles
ModuleNotFoundError: No module named 'aiofiles'

2025-09-01 16:21:25,569 - ServiceManager - INFO - ================================================================================
2025-09-01 16:21:25,570 - ServiceManager - INFO - 🚀 启动分布式文件处理系统服务
2025-09-01 16:21:25,571 - ServiceManager - INFO - ================================================================================
2025-09-01 16:21:25,572 - ServiceManager - INFO - 📋 步骤1: 验证配置文件
2025-09-01 16:21:25,572 - ConfigValidator - INFO - 开始验证配置文件: config.ini
2025-09-01 16:21:25,576 - ConfigValidator - WARNING - 配置验证发现警告:
2025-09-01 16:21:25,576 - ConfigValidator - WARNING -   ⚠️ 数据库路径不一致: store.db_path='store.db', database.db_path='data.db'
2025-09-01 16:21:25,577 - ConfigValidator - WARNING -   ⚠️ 监控目录不存在: /home/<USER>/SourceData
2025-09-01 16:21:25,577 - ConfigValidator - WARNING -   ⚠️ 监控目录不存在: /home/<USER>/dataShare
2025-09-01 16:21:25,577 - ServiceManager - INFO - ✅ 配置验证通过
2025-09-01 16:21:25,578 - ServiceManager - INFO - 🗄️ 步骤2: 准备数据库
2025-09-01 16:21:25,578 - DatabaseManager - INFO - 检查数据库WAL模式: store.db
2025-09-01 16:21:25,599 - DatabaseManager - INFO - 数据库已启用WAL模式
2025-09-01 16:21:25,599 - DatabaseManager - INFO - 数据库并发优化参数设置完成
2025-09-01 16:21:25,601 - DatabaseManager - INFO - 测试数据库并发访问...
2025-09-01 16:21:25,608 - DatabaseManager - INFO - ✅ 数据库并发访问测试通过
2025-09-01 16:21:25,610 - ServiceManager - INFO - ✅ 数据库准备完成
2025-09-01 16:21:25,611 - ServiceManager - INFO - 📊 步骤3: 配置摘要
2025-09-01 16:21:25,611 - ServiceManager - INFO -   📡 Store服务:
2025-09-01 16:21:25,611 - ServiceManager - INFO -     - ZeroMQ地址: tcp://0.0.0.0:13232
2025-09-01 16:21:25,612 - ServiceManager - INFO -     - 监控目录: 2 个
2025-09-01 16:21:25,612 - ServiceManager - INFO -     - 工作线程: 4
2025-09-01 16:21:25,613 - ServiceManager - INFO -     - 数据库连接池: 10
2025-09-01 16:21:25,614 - ServiceManager - INFO -   🔍 Search服务:
2025-09-01 16:21:25,614 - ServiceManager - INFO -     - HTTP地址: http://0.0.0.0:8001
2025-09-01 16:21:25,615 - ServiceManager - INFO -     - API文档: http://0.0.0.0:8001/docs
2025-09-01 16:21:25,615 - ServiceManager - INFO -     - 文件保存: ./search_results
2025-09-01 16:21:25,616 - ServiceManager - INFO -   🗄️ 数据库:
2025-09-01 16:21:25,616 - ServiceManager - INFO -     - 路径: store.db
2025-09-01 16:21:25,616 - ServiceManager - INFO -     - 模式: WAL (支持并发)
2025-09-01 16:21:25,616 - ServiceManager - INFO - 🎯 步骤4: 启动服务
2025-09-01 16:21:25,617 - ServiceManager - INFO -   📡 启动Store服务...
2025-09-01 16:21:27,621 - ServiceManager - ERROR -   ❌ Store服务启动失败:
2025-09-01 16:21:27,622 - ServiceManager - ERROR -     错误: 2025-09-01 16:21:25,737 - StoreService - INFO - 配置加载完成
2025-09-01 16:21:25,738 - StoreService - INFO -   数据库路径: store.db
2025-09-01 16:21:25,738 - StoreService - INFO -   监控目录: ['/home/<USER>/SourceData', '/home/<USER>/dataShare']
2025-09-01 16:21:25,739 - StoreService - INFO -   集群节点: ['192.168.10.61', '192.168.10.62', '192.168.10.63', '192.168.10.64', '192.168.10.66', '192.168.10.67']
2025-09-01 16:21:25,740 - StoreService - INFO -   工作线程数: 4
2025-09-01 16:21:25,740 - StoreService - INFO - ============================================================
2025-09-01 16:21:25,741 - StoreService - INFO - 🚀 启动分布式数据收集和存储服务
2025-09-01 16:21:25,742 - StoreService - INFO - ============================================================
2025-09-01 16:21:25,816 - DatabaseManager - INFO - 源示例数据插入完成: 成功 5 条, 失败 0 条
2025-09-01 16:21:25,868 - DatabaseManager - INFO - 真值示例数据插入完成: 成功 10 条, 失败 0 条
2025-09-01 16:21:25,908 - DatabaseManager - INFO - 特征示例数据插入完成: 成功 15 条, 失败 0 条
2025-09-01 16:21:25,908 - DatabaseManager - INFO - 时间戳为：111
2025-09-01 16:21:25,909 - DatabaseManager - INFO - 时间戳为：112
2025-09-01 16:21:25,910 - DatabaseManager - INFO - 时间戳为：113
2025-09-01 16:21:25,910 - DatabaseManager - INFO - 时间戳为：114
2025-09-01 16:21:25,910 - DatabaseManager - INFO - 时间戳为：115
2025-09-01 16:21:25,911 - DatabaseManager - INFO - 时间戳为：116
2025-09-01 16:21:25,911 - DatabaseManager - INFO - 时间戳为：117
2025-09-01 16:21:25,912 - DatabaseManager - INFO - 时间戳为：118
2025-09-01 16:21:25,912 - DatabaseManager - INFO - 时间戳为：119
2025-09-01 16:21:25,913 - DatabaseManager - INFO - 时间戳为：120
2025-09-01 16:21:25,915 - DatabaseManager - INFO - 决策示例数据插入完成: 成功 10 条, 失败 0 条
2025-09-01 16:21:25,916 - DatabaseManager - INFO - 数据库表结构初始化完成
2025-09-01 16:21:25,916 - DatabaseManager - INFO - 数据库管理器初始化完成: /mnt/hgfs/ShareData/502/dataShare/database.db
2025-09-01 16:21:25,917 - StoreService - INFO - 核心组件设置完成
2025-09-01 16:21:25,917 - StoreService - INFO - 启动数据处理线程...
2025-09-01 16:21:25,918 - DataProcessor - INFO - 数据库表结构设置完成
2025-09-01 16:21:25,919 - DataProcessor - INFO - 数据处理工作线程启动: DataProcessor-Worker
2025-09-01 16:21:25,919 - DataProcessor - INFO - 数据处理线程启动成功，线程池大小: 4
2025-09-01 16:21:25,919 - StoreService - INFO - 启动目录监控线程...
2025-09-01 16:21:25,920 - DirectoryMonitor - ERROR - 设置ZeroMQ发布器失败: Address already in use (addr='tcp://0.0.0.0:13232')
2025-09-01 16:21:25,921 - DirectoryMonitor - ERROR - 启动目录监控线程失败: ZeroMQ发布器设置失败
2025-09-01 16:21:25,922 - StoreService - ERROR - 启动存储服务失败: ZeroMQ发布器设置失败
2025-09-01 16:21:25,922 - StoreService - INFO - ============================================================
2025-09-01 16:21:25,923 - StoreService - INFO - 🛑 停止分布式数据收集和存储服务
2025-09-01 16:21:25,923 - StoreService - INFO - ============================================================
2025-09-01 16:21:25,924 - StoreService - INFO - 停止网络订阅线程...
2025-09-01 16:21:25,924 - NetworkSubscriber - INFO - 网络订阅线程已停止
2025-09-01 16:21:25,925 - StoreService - INFO - 停止目录监控线程...
2025-09-01 16:21:25,926 - DirectoryMonitor - INFO - 目录监控线程已停止
2025-09-01 16:21:25,926 - StoreService - INFO - 停止数据处理线程...
2025-09-01 16:21:25,927 - DataProcessor - INFO - 数据处理工作线程退出: DataProcessor-Worker
2025-09-01 16:21:25,927 - DataProcessor - INFO - 数据处理线程已停止
2025-09-01 16:21:25,930 - DatabaseManager - INFO - 数据库连接池已关闭
2025-09-01 16:21:25,931 - StoreService - INFO - ============================================================
2025-09-01 16:21:25,932 - StoreService - INFO - ✅ 存储服务已安全停止
2025-09-01 16:21:25,932 - StoreService - INFO - ============================================================
2025-09-01 16:21:25,933 - StoreService - ERROR - 服务运行失败: ZeroMQ发布器设置失败

