#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一服务管理器 - 一键启动和管理 store.py 和 search.py 服务
提供配置验证、数据库并发安全、服务生命周期管理等功能

创建时间: 2025-01-25
"""

import argparse
import asyncio
import configparser
import logging
import os
import signal
import sqlite3
import sys
import time
from pathlib import Path
from typing import Dict, Any, Optional, List
import threading
import subprocess


class ConfigValidator:
    """配置文件验证器"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.logger = logging.getLogger('ConfigValidator')
        self.config = None
        self.validation_errors = []
        self.validation_warnings = []
    
    def validate(self) -> bool:
        """验证配置文件完整性和正确性"""
        try:
            self.logger.info(f"开始验证配置文件: {self.config_path}")
            
            # 检查配置文件是否存在
            if not os.path.exists(self.config_path):
                self.validation_errors.append(f"配置文件不存在: {self.config_path}")
                return False
            
            # 加载配置文件
            self.config = configparser.ConfigParser()
            self.config.read(self.config_path, encoding='utf-8')
            
            # 验证各个配置节
            self._validate_store_config()
            self._validate_search_config()
            self._validate_database_config()
            self._validate_port_conflicts()
            self._validate_directories()
            
            # 输出验证结果
            self._print_validation_results()
            
            return len(self.validation_errors) == 0
            
        except Exception as e:
            self.validation_errors.append(f"配置文件验证异常: {e}")
            return False
    
    def _validate_store_config(self):
        """验证store配置节"""
        if not self.config.has_section('store'):
            self.validation_errors.append("缺少 [store] 配置节")
            return
        
        store_config = self.config['store']
        required_keys = [
            'db_path', 'max_connections', 'watch_directories',
            'zmq_publish_addr', 'max_workers', 'queue_maxsize'
        ]
        
        for key in required_keys:
            if key not in store_config:
                self.validation_errors.append(f"[store] 节缺少必需配置: {key}")
        
        # 验证数值配置
        try:
            max_connections = int(store_config.get('max_connections', '10'))
            if max_connections < 1 or max_connections > 100:
                self.validation_warnings.append("store.max_connections 建议设置在 1-100 之间")
        except ValueError:
            self.validation_errors.append("store.max_connections 必须是有效整数")
        
        try:
            max_workers = int(store_config.get('max_workers', '4'))
            if max_workers < 1 or max_workers > 20:
                self.validation_warnings.append("store.max_workers 建议设置在 1-20 之间")
        except ValueError:
            self.validation_errors.append("store.max_workers 必须是有效整数")
    
    def _validate_search_config(self):
        """验证search配置节"""
        if not self.config.has_section('search'):
            self.validation_errors.append("缺少 [search] 配置节")
            return
        
        search_config = self.config['search']
        required_keys = ['host', 'port', 'save_directory', 'file_prefix']
        
        for key in required_keys:
            if key not in search_config:
                self.validation_errors.append(f"[search] 节缺少必需配置: {key}")
        
        # 验证端口配置
        try:
            port = int(search_config.get('port', '8001'))
            if port < 1024 or port > 65535:
                self.validation_errors.append("search.port 必须在 1024-65535 范围内")
        except ValueError:
            self.validation_errors.append("search.port 必须是有效整数")
    
    def _validate_database_config(self):
        """验证数据库配置一致性"""
        if not (self.config.has_section('store') and self.config.has_section('search')):
            return
        
        # 注意：search服务通过database.py访问数据库，需要检查database配置
        if self.config.has_section('database'):
            db_config = self.config['database']
            store_db_path = self.config['store'].get('db_path', 'store.db')
            database_db_path = db_config.get('db_path', 'data.db')
            
            if store_db_path != database_db_path:
                self.validation_warnings.append(
                    f"数据库路径不一致: store.db_path='{store_db_path}', "
                    f"database.db_path='{database_db_path}'"
                )
    
    def _validate_port_conflicts(self):
        """验证端口冲突"""
        ports_used = {}
        
        # 检查store的ZeroMQ端口
        if self.config.has_section('store'):
            zmq_addr = self.config['store'].get('zmq_publish_addr', 'tcp://0.0.0.0:13232')
            try:
                port = int(zmq_addr.split(':')[-1])
                ports_used['store_zmq'] = port
            except (ValueError, IndexError):
                self.validation_errors.append(f"store.zmq_publish_addr 格式无效: {zmq_addr}")
        
        # 检查search的HTTP端口
        if self.config.has_section('search'):
            try:
                port = int(self.config['search'].get('port', '8001'))
                if port in ports_used.values():
                    self.validation_errors.append(f"端口冲突: search服务端口 {port} 已被使用")
                ports_used['search_http'] = port
            except ValueError:
                pass  # 已在前面检查过
        
        # 检查api服务端口（如果存在）
        if self.config.has_section('api'):
            try:
                port = int(self.config['api'].get('port', '8000'))
                if port in ports_used.values():
                    self.validation_errors.append(f"端口冲突: api服务端口 {port} 已被使用")
            except ValueError:
                pass
    
    def _validate_directories(self):
        """验证目录路径和权限"""
        # 验证store监控目录
        if self.config.has_section('store'):
            watch_dirs = self.config['store'].get('watch_directories', '').split(';')
            for watch_dir in watch_dirs:
                watch_dir = watch_dir.strip()
                if watch_dir and not os.path.exists(watch_dir):
                    self.validation_warnings.append(f"监控目录不存在: {watch_dir}")
                elif watch_dir and not os.access(watch_dir, os.R_OK):
                    self.validation_errors.append(f"监控目录无读权限: {watch_dir}")
        
        # 验证search保存目录
        if self.config.has_section('search'):
            save_dir = self.config['search'].get('save_directory', './search_results')
            parent_dir = os.path.dirname(os.path.abspath(save_dir))
            if not os.path.exists(parent_dir):
                self.validation_errors.append(f"search保存目录的父目录不存在: {parent_dir}")
            elif not os.access(parent_dir, os.W_OK):
                self.validation_errors.append(f"search保存目录无写权限: {parent_dir}")
    
    def _print_validation_results(self):
        """打印验证结果"""
        if self.validation_errors:
            self.logger.error("配置验证发现错误:")
            for error in self.validation_errors:
                self.logger.error(f"  ❌ {error}")
        
        if self.validation_warnings:
            self.logger.warning("配置验证发现警告:")
            for warning in self.validation_warnings:
                self.logger.warning(f"  ⚠️ {warning}")
        
        if not self.validation_errors and not self.validation_warnings:
            self.logger.info("✅ 配置验证通过，所有配置项正确")


class DatabaseManager:
    """数据库并发安全管理器"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.logger = logging.getLogger('DatabaseManager')
    
    def ensure_wal_mode(self) -> bool:
        """确保数据库启用WAL模式以支持并发访问"""
        try:
            self.logger.info(f"检查数据库WAL模式: {self.db_path}")
            
            # 如果数据库文件不存在，创建它
            if not os.path.exists(self.db_path):
                self.logger.info("数据库文件不存在，将在首次连接时创建")
                # 创建目录（如果需要）
                os.makedirs(os.path.dirname(os.path.abspath(self.db_path)), exist_ok=True)
            
            # 连接数据库并设置WAL模式
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            try:
                # 检查当前journal模式
                cursor = conn.cursor()
                cursor.execute("PRAGMA journal_mode")
                current_mode = cursor.fetchone()[0]
                
                if current_mode.upper() != 'WAL':
                    self.logger.info(f"当前journal模式: {current_mode}，正在切换到WAL模式")
                    cursor.execute("PRAGMA journal_mode=WAL")
                    new_mode = cursor.fetchone()[0]
                    self.logger.info(f"WAL模式设置成功: {new_mode}")
                else:
                    self.logger.info("数据库已启用WAL模式")
                
                # 设置其他并发优化参数
                cursor.execute("PRAGMA synchronous=NORMAL")
                cursor.execute("PRAGMA cache_size=-64000")  # 64MB缓存
                cursor.execute("PRAGMA temp_store=MEMORY")
                cursor.execute("PRAGMA mmap_size=268435456")  # 256MB内存映射
                cursor.execute("PRAGMA busy_timeout=30000")  # 30秒超时
                
                conn.commit()
                self.logger.info("数据库并发优化参数设置完成")
                return True
                
            finally:
                conn.close()
                
        except Exception as e:
            self.logger.error(f"设置数据库WAL模式失败: {e}")
            return False
    
    def test_concurrent_access(self) -> bool:
        """测试数据库并发访问"""
        try:
            self.logger.info("测试数据库并发访问...")
            
            # 创建两个并发连接
            conn1 = sqlite3.connect(self.db_path, timeout=10.0)
            conn2 = sqlite3.connect(self.db_path, timeout=10.0)
            
            try:
                # 测试并发读取
                cursor1 = conn1.cursor()
                cursor2 = conn2.cursor()
                
                cursor1.execute("SELECT name FROM sqlite_master WHERE type='table'")
                cursor2.execute("SELECT name FROM sqlite_master WHERE type='table'")
                
                tables1 = cursor1.fetchall()
                tables2 = cursor2.fetchall()
                
                self.logger.info("✅ 数据库并发访问测试通过")
                return True
                
            finally:
                conn1.close()
                conn2.close()
                
        except Exception as e:
            self.logger.error(f"数据库并发访问测试失败: {e}")
            return False


class ServiceManager:
    """统一服务管理器"""

    def __init__(self, config_path: str = "config.ini"):
        self.config_path = config_path
        self.logger = logging.getLogger('ServiceManager')

        # 服务状态
        self.running = False
        self.store_process = None
        self.search_process = None
        self.services_started = []

        # 配置和管理器
        self.config_validator = None
        self.database_manager = None
        self.config = None

        # 设置日志
        self._setup_logging()

    def _setup_logging(self):
        """设置日志配置"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('service.log', encoding='utf-8')
            ]
        )

    async def start_services(self) -> bool:
        """启动所有服务"""
        try:
            self.logger.info("=" * 80)
            self.logger.info("🚀 启动分布式文件处理系统服务")
            self.logger.info("=" * 80)

            # 步骤1: 验证配置
            if not await self._validate_configuration():
                return False

            # 步骤2: 准备数据库
            if not await self._prepare_database():
                return False

            # 步骤3: 显示配置摘要
            self._display_configuration_summary()

            # 步骤4: 启动服务
            if not await self._start_individual_services():
                return False

            # 步骤5: 验证服务状态
            if not await self._verify_services_health():
                return False

            self.running = True
            self._display_startup_success()
            return True

        except Exception as e:
            self.logger.error(f"启动服务失败: {e}")
            await self._cleanup_services()
            return False

    async def _validate_configuration(self) -> bool:
        """验证配置文件"""
        self.logger.info("📋 步骤1: 验证配置文件")

        self.config_validator = ConfigValidator(self.config_path)
        if not self.config_validator.validate():
            self.logger.error("❌ 配置验证失败，请修复配置文件后重试")
            return False

        self.config = self.config_validator.config
        self.logger.info("✅ 配置验证通过")
        return True

    async def _prepare_database(self) -> bool:
        """准备数据库"""
        self.logger.info("🗄️ 步骤2: 准备数据库")

        # 获取数据库路径
        db_path = self.config['store'].get('db_path', 'store.db')

        self.database_manager = DatabaseManager(db_path)

        # 设置WAL模式
        if not self.database_manager.ensure_wal_mode():
            self.logger.error("❌ 数据库WAL模式设置失败")
            return False

        # 测试并发访问
        if not self.database_manager.test_concurrent_access():
            self.logger.error("❌ 数据库并发访问测试失败")
            return False

        self.logger.info("✅ 数据库准备完成")
        return True

    def _display_configuration_summary(self):
        """显示配置摘要"""
        self.logger.info("📊 步骤3: 配置摘要")

        # Store服务配置
        store_config = self.config['store']
        zmq_addr = store_config.get('zmq_publish_addr', 'tcp://0.0.0.0:13232')
        watch_dirs = store_config.get('watch_directories', '').split(';')

        self.logger.info(f"  📡 Store服务:")
        self.logger.info(f"    - ZeroMQ地址: {zmq_addr}")
        self.logger.info(f"    - 监控目录: {len([d for d in watch_dirs if d.strip()])} 个")
        self.logger.info(f"    - 工作线程: {store_config.get('max_workers', '4')}")
        self.logger.info(f"    - 数据库连接池: {store_config.get('max_connections', '10')}")

        # Search服务配置
        search_config = self.config['search']
        search_host = search_config.get('host', '0.0.0.0')
        search_port = search_config.get('port', '8001')
        save_dir = search_config.get('save_directory', './search_results')

        self.logger.info(f"  🔍 Search服务:")
        self.logger.info(f"    - HTTP地址: http://{search_host}:{search_port}")
        self.logger.info(f"    - API文档: http://{search_host}:{search_port}/docs")
        self.logger.info(f"    - 文件保存: {save_dir}")

        # 数据库配置
        db_path = store_config.get('db_path', 'store.db')
        self.logger.info(f"  🗄️ 数据库:")
        self.logger.info(f"    - 路径: {db_path}")
        self.logger.info(f"    - 模式: WAL (支持并发)")

    async def _start_individual_services(self) -> bool:
        """启动各个服务"""
        self.logger.info("🎯 步骤4: 启动服务")

        # 检查服务启用状态
        store_enabled = self.config['store'].get('service_enabled', 'true').lower() == 'true'
        search_enabled = self.config['search'].get('search_enable', 'true').lower() == 'true'

        if not store_enabled and not search_enabled:
            self.logger.error("❌ 所有服务都被禁用")
            return False

        # 启动Store服务
        if store_enabled:
            if not await self._start_store_service():
                return False
        else:
            self.logger.info("⏭️ Store服务已禁用，跳过启动")

        # 等待一段时间确保Store服务完全启动
        if store_enabled:
            self.logger.info("⏳ 等待Store服务完全启动...")
            await asyncio.sleep(3)

        # 启动Search服务
        if search_enabled:
            if not await self._start_search_service():
                return False
        else:
            self.logger.info("⏭️ Search服务已禁用，跳过启动")

        return True

    async def _start_store_service(self) -> bool:
        """启动Store服务"""
        try:
            self.logger.info("  📡 启动Store服务...")

            # 使用subprocess启动store.py
            self.store_process = subprocess.Popen(
                [sys.executable, 'store.py'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            # 等待一段时间检查进程是否正常启动
            await asyncio.sleep(2)

            if self.store_process.poll() is None:
                self.services_started.append('store')
                self.logger.info("  ✅ Store服务启动成功")
                return True
            else:
                stdout, stderr = self.store_process.communicate()
                self.logger.error(f"  ❌ Store服务启动失败:")
                if stderr:
                    self.logger.error(f"    错误: {stderr}")
                return False

        except Exception as e:
            self.logger.error(f"  ❌ Store服务启动异常: {e}")
            return False

    async def _start_search_service(self) -> bool:
        """启动Search服务"""
        try:
            self.logger.info("  🔍 启动Search服务...")

            # 使用subprocess启动search.py
            self.search_process = subprocess.Popen(
                [sys.executable, 'search.py'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            # 等待一段时间检查进程是否正常启动
            await asyncio.sleep(2)

            if self.search_process.poll() is None:
                self.services_started.append('search')
                self.logger.info("  ✅ Search服务启动成功")
                return True
            else:
                stdout, stderr = self.search_process.communicate()
                self.logger.error(f"  ❌ Search服务启动失败:")
                if stderr:
                    self.logger.error(f"    错误: {stderr}")
                return False

        except Exception as e:
            self.logger.error(f"  ❌ Search服务启动异常: {e}")
            return False

    async def _verify_services_health(self) -> bool:
        """验证服务健康状态"""
        self.logger.info("🏥 步骤5: 验证服务健康状态")

        all_healthy = True

        # 检查Store服务
        if 'store' in self.services_started:
            if self.store_process and self.store_process.poll() is None:
                self.logger.info("  ✅ Store服务运行正常")
            else:
                self.logger.error("  ❌ Store服务运行异常")
                all_healthy = False

        # 检查Search服务
        if 'search' in self.services_started:
            if self.search_process and self.search_process.poll() is None:
                # 尝试访问Search服务的健康检查端点
                try:
                    import requests
                    search_port = self.config['search'].get('port', '8001')
                    response = requests.get(f"http://localhost:{search_port}/", timeout=5)
                    if response.status_code == 200:
                        self.logger.info("  ✅ Search服务运行正常")
                    else:
                        self.logger.warning(f"  ⚠️ Search服务响应异常: {response.status_code}")
                except Exception as e:
                    self.logger.warning(f"  ⚠️ Search服务健康检查失败: {e}")
                    # 不标记为失败，因为服务可能还在启动中
            else:
                self.logger.error("  ❌ Search服务运行异常")
                all_healthy = False

        return all_healthy

    def _display_startup_success(self):
        """显示启动成功信息"""
        self.logger.info("=" * 80)
        self.logger.info("🎉 所有服务启动成功！")
        self.logger.info("=" * 80)

        # 显示服务访问信息
        if 'store' in self.services_started:
            zmq_addr = self.config['store'].get('zmq_publish_addr', 'tcp://0.0.0.0:13232')
            self.logger.info(f"📡 Store服务:")
            self.logger.info(f"   ZeroMQ发布地址: {zmq_addr}")
            self.logger.info(f"   数据收集和存储功能已启用")

        if 'search' in self.services_started:
            search_host = self.config['search'].get('host', '0.0.0.0')
            search_port = self.config['search'].get('port', '8001')
            save_dir = self.config['search'].get('save_directory', './search_results')

            self.logger.info(f"🔍 Search服务:")
            self.logger.info(f"   HTTP服务: http://{search_host}:{search_port}")
            self.logger.info(f"   API文档: http://{search_host}:{search_port}/docs")
            self.logger.info(f"   查询结果保存: {save_dir}")

        self.logger.info("=" * 80)
        self.logger.info("💡 使用说明:")
        self.logger.info("   - 按 Ctrl+C 可安全停止所有服务")
        self.logger.info("   - 服务日志保存在 service.log 文件中")
        self.logger.info("   - 各服务的详细日志请查看对应的日志文件")
        self.logger.info("=" * 80)

    async def stop_services(self):
        """停止所有服务"""
        try:
            if not self.running:
                return

            self.running = False
            self.logger.info("=" * 80)
            self.logger.info("🛑 正在停止所有服务...")
            self.logger.info("=" * 80)

            # 停止Search服务
            if self.search_process and self.search_process.poll() is None:
                self.logger.info("🔍 停止Search服务...")
                self.search_process.terminate()
                try:
                    self.search_process.wait(timeout=10)
                    self.logger.info("  ✅ Search服务已停止")
                except subprocess.TimeoutExpired:
                    self.logger.warning("  ⚠️ Search服务未响应，强制终止")
                    self.search_process.kill()
                    self.search_process.wait()

            # 停止Store服务
            if self.store_process and self.store_process.poll() is None:
                self.logger.info("📡 停止Store服务...")
                self.store_process.terminate()
                try:
                    self.store_process.wait(timeout=10)
                    self.logger.info("  ✅ Store服务已停止")
                except subprocess.TimeoutExpired:
                    self.logger.warning("  ⚠️ Store服务未响应，强制终止")
                    self.store_process.kill()
                    self.store_process.wait()

            self.logger.info("=" * 80)
            self.logger.info("✅ 所有服务已安全停止")
            self.logger.info("=" * 80)

        except Exception as e:
            self.logger.error(f"停止服务时出错: {e}")

    async def _cleanup_services(self):
        """清理服务（发生错误时调用）"""
        self.logger.info("🧹 清理服务...")
        await self.stop_services()

    async def monitor_services(self):
        """监控服务状态"""
        while self.running:
            try:
                # 检查服务进程状态
                if 'store' in self.services_started:
                    if self.store_process and self.store_process.poll() is not None:
                        self.logger.error("❌ Store服务意外退出")
                        self.running = False
                        break

                if 'search' in self.services_started:
                    if self.search_process and self.search_process.poll() is not None:
                        self.logger.error("❌ Search服务意外退出")
                        self.running = False
                        break

                # 等待一段时间再次检查
                await asyncio.sleep(5)

            except Exception as e:
                self.logger.error(f"监控服务时出错: {e}")
                break

        # 如果监控循环退出，停止所有服务
        if self.running:
            await self.stop_services()


async def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='分布式文件处理系统服务管理器')
    parser.add_argument('--config', '-c', default='config.ini',
                       help='配置文件路径 (默认: config.ini)')
    parser.add_argument('--check-only', action='store_true',
                       help='仅检查配置，不启动服务')

    args = parser.parse_args()

    # 创建服务管理器
    service_manager = ServiceManager(args.config)

    # 如果只是检查配置
    if args.check_only:
        print("🔍 配置检查模式")
        validator = ConfigValidator(args.config)
        if validator.validate():
            print("✅ 配置检查通过")
            sys.exit(0)
        else:
            print("❌ 配置检查失败")
            sys.exit(1)

    # 设置信号处理器
    def signal_handler(signum, frame):
        print(f"\n🛑 收到信号 {signum}，正在安全关闭所有服务...")
        service_manager.running = False
        # 创建新的事件循环来处理停止操作
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(service_manager.stop_services())
            loop.close()
        except Exception as e:
            print(f"❌ 停止服务时出错: {e}")
        finally:
            print("👋 服务管理器已退出")
            sys.exit(0)

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # 终止信号

    try:
        # 启动服务
        if await service_manager.start_services():
            # 监控服务状态
            await service_manager.monitor_services()
        else:
            print("❌ 服务启动失败")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n🛑 收到中断信号，正在关闭服务...")
    except Exception as e:
        print(f"❌ 服务运行失败: {e}")
        service_manager.logger.error(f"服务运行失败: {e}")
    finally:
        await service_manager.stop_services()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 程序运行失败: {e}")
        sys.exit(1)
