# 搜索服务模块 (search.py)

## 概述

搜索服务模块是基于现有 `api.py` 文件创建的新模块，提供相同的 RESTful API 接口，但移除了 ZeroMQ 消息队列功能，改为将查询结果直接保存到本地文件系统。

## 主要特性

- ✅ **完整API兼容**: 实现了 `api.py` 中的所有 RESTful API 接口
- ✅ **移除ZeroMQ**: 不依赖 ZeroMQ 消息队列，简化部署
- ✅ **文件保存**: 所有查询结果自动保存为 JSON 格式文件
- ✅ **独立端口**: 使用独立端口 (8001)，避免与原 API 服务冲突
- ✅ **配置管理**: 通过 `config.ini` 管理所有配置参数
- ✅ **完整日志**: 提供详细的操作日志和错误处理

## 配置说明

在 `config.ini` 文件中的 `[search]` 配置节：

```ini
[search]
# 服务配置
host = 0.0.0.0
port = 8001

# 文件保存配置
save_directory = ./search_results
file_prefix = search

# 服务启用状态
search_enable = true
```

### 配置参数说明

- `host`: 服务绑定的主机地址
- `port`: 服务端口 (默认 8001，避免与 api.py 的 8000 端口冲突)
- `save_directory`: 查询结果文件保存的根目录
- `file_prefix`: 保存文件的前缀名称
- `search_enable`: 服务启用开关

## API 接口

### 1. 根路径健康检查
```
GET /
```

### 2. 位置转换查询
```
GET /tasks/position/?device_id={卫星ID}&timestamp={时间戳}
```

### 3. 融合定位查询
```
GET /tasks/fusion/?device_id={卫星ID}&timestamp={时间戳}
```

### 4. 意图分析查询
```
GET /tasks/intention/?style={样式}
```

### 5. 电侦智能查询
```
GET /tasks/XDZN/?device_id={卫星ID}&timestamp={时间戳}
```

### 6. 雷达智能查询
```
GET /tasks/BKZN/?device_id={卫星ID}&timestamp={时间戳}
```

### 7. 原始数据查询
```
GET /data/origional/?timestamps={时间戳列表}
```

### 8. 特征数据查询
```
GET /data/feature/?timestamps={时间戳列表}
```

### 9. 决策数据查询
```
GET /data/decision/?timestamps={时间戳列表}
```

## 文件命名规则

保存的文件按以下规则命名：

1. **带设备ID和时间戳的查询**:
   ```
   {file_prefix}_{query_type}_{device_id}_{timestamp}_{current_time}.json
   ```
   例如: `search_WZZH_SAT001_1640995200000_20250125_143022_123.json`

2. **带样式参数的查询**:
   ```
   {file_prefix}_{query_type}_{style}_{current_time}.json
   ```
   例如: `search_YTFX_7_20250125_143022_123.json`

3. **通用数据查询**:
   ```
   {file_prefix}_{query_type}_{current_time}.json
   ```
   例如: `search_origional_data_20250125_143022_123.json`

## 启动服务

### 独立启动
```bash
python search.py
```

### 查看服务状态
访问: http://localhost:8001

### 查看API文档
访问: http://localhost:8001/docs

## 测试服务

使用提供的测试脚本：

```bash
python test_search.py
```

测试脚本会：
- 检查服务状态
- 测试所有API接口
- 验证文件保存功能
- 生成测试报告

## 与原 API 服务的区别

| 特性 | api.py | search.py |
|------|--------|-----------|
| ZeroMQ 支持 | ✅ | ❌ |
| 文件保存 | ❌ | ✅ |
| 默认端口 | 8000 | 8001 |
| 消息发布 | ✅ | ❌ |
| 直接响应 | ✅ | ✅ |
| 配置节 | [api] | [search] |

## 使用场景

1. **离线数据分析**: 需要保存查询结果进行后续分析
2. **数据备份**: 自动备份重要查询结果
3. **简化部署**: 不需要 ZeroMQ 基础设施
4. **批量处理**: 收集查询结果进行批量处理
5. **审计追踪**: 保留查询历史记录

## 目录结构

```
./
├── search.py              # 搜索服务主文件
├── test_search.py         # 测试脚本
├── config.ini            # 配置文件 (包含 [search] 节)
├── SEARCH_README.md      # 本文档
└── search_results/       # 查询结果保存目录 (自动创建)
    ├── search_WZZH_*.json
    ├── search_RHDW_*.json
    └── ...
```

## 注意事项

1. **端口冲突**: 确保 8001 端口未被占用
2. **磁盘空间**: 查询结果文件会占用磁盘空间，需定期清理
3. **权限**: 确保对保存目录有写权限
4. **数据库**: 依赖相同的数据库配置和连接
5. **并发**: 支持多客户端并发查询

## 故障排除

### 服务无法启动
- 检查端口 8001 是否被占用
- 检查配置文件 `config.ini` 是否正确
- 检查数据库连接是否正常

### 文件保存失败
- 检查保存目录权限
- 检查磁盘空间
- 查看服务日志

### API 查询失败
- 检查数据库中是否有对应数据
- 检查查询参数格式
- 查看错误日志

## 日志文件

服务运行时会生成日志，包含：
- 请求处理信息
- 文件保存状态
- 错误和异常信息
- 性能统计数据
