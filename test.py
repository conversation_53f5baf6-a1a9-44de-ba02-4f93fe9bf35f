import sqlite3

def printTable(path):
    conn = sqlite3.connect(path)
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()

    for table in tables:
        table_name = table[0]
        print(f"\n{'='*60}")
        print(f"Table: {table_name}")
        print(f"{'='*60}")

        # 获取列信息
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]  # 提取列名

        print("\nColumns:")
        for i, column in enumerate(columns):
            print(f"    {i+1}. {column[1]} ({column[2]}) {'NOT NULL' if column[3] else 'NULL'}")
        
        # 获取表中的数据行数
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        row_count = cursor.fetchone()[0]
        print(f"\nTotal rows: {row_count}")
        
        if row_count > 0:
            print(f"\nTable Content (showing first 10 rows):")
            print("-" * 80)
            
            # 获取实际数据 (限制前10行)
            cursor.execute(f"SELECT * FROM {table_name} LIMIT 10")
            rows = cursor.fetchall()
            
            # 打印列标题
            header = " | ".join([f"{name:15}" for name in column_names])
            print(header)
            print("-" * len(header))
            
            # 打印数据行
            for row in rows:
                row_str = " | ".join([f"{str(val)}" for val in row])
                print(row_str)
                
            if row_count > 10:
                print(f"\n... and {row_count - 10} more rows")
        else:
            print("\nTable is empty.")

    conn.close()
    print(f"\n{'='*60}")
    print("Database inspection completed.")
    print(f"{'='*60}")

printTable("./database.db")