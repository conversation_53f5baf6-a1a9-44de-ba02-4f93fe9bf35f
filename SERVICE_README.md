# 服务管理器 (service.py)

## 概述

`service.py` 是分布式文件处理系统的统一服务管理器，能够一键启动和管理 `store.py` 和 `search.py` 两个服务，提供配置验证、数据库并发安全、服务生命周期管理等功能。

## 主要功能

### 🔧 配置验证
- ✅ 验证 `config.ini` 中 `[store]` 和 `[search]` 配置节的完整性
- ✅ 检查数据库路径配置一致性
- ✅ 验证端口配置避免冲突
- ✅ 检查目录路径存在性和权限
- ✅ 提供详细的错误信息和修复建议

### 🗄️ 数据库并发安全
- ✅ 自动启用 SQLite WAL 模式支持并发读写
- ✅ 优化数据库连接参数
- ✅ 测试并发访问能力
- ✅ 确保两个服务安全共享数据库

### 🚀 服务生命周期管理
- ✅ 按正确顺序启动服务（store → search）
- ✅ 优雅关闭机制（Ctrl+C 安全停止）
- ✅ 服务健康检查和状态监控
- ✅ 自动故障检测和恢复

### 📊 监控和日志
- ✅ 详细的启动日志和进度显示
- ✅ 服务状态实时监控
- ✅ 统一的日志管理
- ✅ 清晰的用户界面

## 使用方法

### 基本启动
```bash
# 使用默认配置文件 config.ini
python service.py

# 指定配置文件
python service.py --config custom_config.ini
```

### 配置检查
```bash
# 仅检查配置，不启动服务
python service.py --check-only

# 检查指定配置文件
python service.py --config custom_config.ini --check-only
```

### 命令行参数
- `--config, -c`: 指定配置文件路径（默认: config.ini）
- `--check-only`: 仅检查配置，不启动服务

## 配置要求

### 必需的配置节

#### [store] 配置节
```ini
[store]
db_path = store.db                    # 数据库文件路径
max_connections = 10                  # 数据库连接池大小
watch_directories = ./data1;./data2   # 监控目录（分号分隔）
zmq_publish_addr = tcp://0.0.0.0:13232  # ZeroMQ发布地址
cluster_nodes =                       # 集群节点（可选）
subscribe_topics = original_data,feature_data  # 订阅主题
max_workers = 4                       # 工作线程数
queue_maxsize = 1000                  # 消息队列大小
service_enabled = true                # 服务启用开关
```

#### [search] 配置节
```ini
[search]
host = 0.0.0.0                       # HTTP服务地址
port = 8001                           # HTTP服务端口
save_directory = ./search_results     # 查询结果保存目录
file_prefix = search                  # 文件前缀
search_enable = true                  # 服务启用开关
```

#### [database] 配置节（可选，用于兼容性检查）
```ini
[database]
db_path = store.db                    # 应与store.db_path一致
max_connections = 20                  # 数据库连接数
```

## 启动流程

### 1. 配置验证阶段
```
📋 步骤1: 验证配置文件
  ✅ 检查配置文件存在性
  ✅ 验证必需配置项
  ✅ 检查数值范围
  ✅ 验证端口冲突
  ✅ 检查目录权限
```

### 2. 数据库准备阶段
```
🗄️ 步骤2: 准备数据库
  ✅ 启用WAL模式
  ✅ 设置并发优化参数
  ✅ 测试并发访问
```

### 3. 配置摘要显示
```
📊 步骤3: 配置摘要
  📡 Store服务配置信息
  🔍 Search服务配置信息
  🗄️ 数据库配置信息
```

### 4. 服务启动阶段
```
🎯 步骤4: 启动服务
  📡 启动Store服务
  ⏳ 等待Store服务稳定
  🔍 启动Search服务
```

### 5. 健康检查阶段
```
🏥 步骤5: 验证服务健康状态
  ✅ 检查进程状态
  ✅ 测试HTTP接口
  ✅ 验证服务响应
```

## 服务信息

### 启动成功后显示的信息
```
🎉 所有服务启动成功！
================================================================================
📡 Store服务:
   ZeroMQ发布地址: tcp://0.0.0.0:13232
   数据收集和存储功能已启用

🔍 Search服务:
   HTTP服务: http://0.0.0.0:8001
   API文档: http://0.0.0.0:8001/docs
   查询结果保存: ./search_results
================================================================================
💡 使用说明:
   - 按 Ctrl+C 可安全停止所有服务
   - 服务日志保存在 service.log 文件中
   - 各服务的详细日志请查看对应的日志文件
================================================================================
```

## 错误处理

### 常见错误和解决方案

#### 1. 配置文件错误
```
❌ 缺少 [store] 配置节
解决方案: 在 config.ini 中添加完整的 [store] 配置节
```

#### 2. 端口冲突
```
❌ 端口冲突: search服务端口 8001 已被使用
解决方案: 修改 search.port 配置为其他可用端口
```

#### 3. 目录权限问题
```
❌ 监控目录无读权限: /path/to/directory
解决方案: 检查目录权限，确保程序有读取权限
```

#### 4. 数据库问题
```
❌ 数据库WAL模式设置失败
解决方案: 检查数据库文件权限和磁盘空间
```

### 服务启动失败处理
- 自动回滚已启动的服务
- 显示详细错误信息
- 提供修复建议
- 保存错误日志

## 监控功能

### 实时监控
- 每5秒检查一次服务进程状态
- 自动检测服务意外退出
- 发现问题时自动停止所有服务

### 日志记录
- 统一日志格式
- 详细的操作记录
- 错误和警告信息
- 保存到 `service.log` 文件

## 优雅关闭

### 信号处理
- 支持 Ctrl+C (SIGINT)
- 支持 SIGTERM 信号
- 自动清理资源

### 关闭流程
1. 接收停止信号
2. 停止Search服务（10秒超时）
3. 停止Store服务（10秒超时）
4. 强制终止未响应的服务
5. 清理临时文件

## 测试验证

### 运行测试
```bash
# 运行单元测试
python test_service.py

# 运行集成测试
python test_service.py integration
```

### 测试内容
- 配置验证功能测试
- 数据库管理功能测试
- 端口冲突检测测试
- WAL模式设置测试
- 并发访问测试

## 最佳实践

### 1. 配置管理
- 使用版本控制管理配置文件
- 为不同环境准备不同配置
- 定期备份配置文件

### 2. 监控运维
- 定期检查日志文件
- 监控磁盘空间使用
- 设置日志轮转

### 3. 性能优化
- 根据硬件调整连接池大小
- 合理设置工作线程数
- 定期清理数据库

### 4. 安全考虑
- 限制网络访问权限
- 定期更新依赖包
- 使用防火墙保护端口

## 故障排除

### 1. 服务无法启动
- 检查配置文件语法
- 验证端口可用性
- 确认目录权限
- 查看详细错误日志

### 2. 数据库问题
- 检查磁盘空间
- 验证文件权限
- 重建数据库索引
- 检查WAL文件

### 3. 网络问题
- 测试端口连通性
- 检查防火墙设置
- 验证网络配置
- 查看网络日志

## 文件结构

```
./
├── service.py              # 服务管理器主文件
├── test_service.py         # 测试脚本
├── config.ini             # 配置文件
├── service.log            # 服务日志
├── store.py               # Store服务
├── search.py              # Search服务
└── SERVICE_README.md      # 本文档
```
