#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件发布模块 - 监控文件系统事件，通过ZeroMQ发布新文件到指定端点
使用Watchdog监控文件系统，高效事件处理

创建时间: 2025-01-25
"""

import configparser
import logging
import os
import signal
import sys
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, Any, List, Set
from queue import Queue, Empty

import zmq
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler


class ZeroMQPublisher:
    """ZeroMQ发布器 - 高性能文件发布"""
    
    def __init__(self, bind_address: str, topics: List[str] = None):
        self.bind_address = bind_address
        self.topics = topics or ['file_updates']
        self.context = None
        self.publisher = None
        self.running = False
        self.publish_count = 0
        self.lock = threading.RLock()
        
        self.logger = logging.getLogger('ZeroMQPublisher')
    
    def start(self) -> bool:
        """启动ZeroMQ发布器"""
        try:
            self.context = zmq.Context()
            self.publisher = self.context.socket(zmq.PUB)
            
            # 设置socket选项
            self.publisher.setsockopt(zmq.SNDHWM, 10000)  # 发送高水位标记
            self.publisher.setsockopt(zmq.LINGER, 1000)   # 关闭时等待时间
            self.publisher.setsockopt(zmq.SNDBUF, 1024 * 1024)  # 发送缓冲区
            
            self.publisher.bind(self.bind_address)
            self.running = True
            
            self.logger.info(f"ZeroMQ发布器已启动: {self.bind_address}")
            return True
            
        except Exception as e:
            self.logger.error(f"启动ZeroMQ发布器失败: {e}")
            return False
    
    def publish_file(self, file_path: str, topic: str = None) -> bool:
        """发布文件"""
        if not self.running or not self.publisher:
            self.logger.error("ZeroMQ发布器未运行")
            return False
        
        try:
            with self.lock:
                # 读取文件内容
                if not os.path.exists(file_path):
                    self.logger.error(f"文件不存在: {file_path}")
                    return False
                
                filename = os.path.basename(file_path)
                file_size = os.path.getsize(file_path)
                
                # 读取文件数据
                with open(file_path, 'rb') as f:
                    file_data = f.read()
                
                # 发布消息 [topic, filename, file_data]
                # 选择主题
                topic_to_use = (
                    topic if isinstance(topic, str) and topic
                    else (
                        self.topics[0]
                        if isinstance(self.topics, list) and len(self.topics) > 0
                        else (self.topics if isinstance(self.topics, str) else 'file_updates')
                    )
                )

                message = [
                    topic_to_use.encode('utf-8'),
                    filename.encode('utf-8'),
                    file_data
                ]
                
                self.publisher.send_multipart(message)
                
                self.publish_count += 1
                self.logger.info(f"文件已发布: {filename} ({file_size} 字节) -> {topic_to_use}")
                return True
                
        except Exception as e:
            self.logger.error(f"发布文件失败 {file_path}: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取发布统计信息"""
        return {
            'running': self.running,
            'bind_address': self.bind_address,
            'topics': self.topics,
            'publish_count': self.publish_count
        }
    
    def stop(self):
        """停止ZeroMQ发布器"""
        self.running = False
        
        if self.publisher:
            self.publisher.close()
            self.publisher = None
        
        if self.context:
            self.context.term()
            self.context = None
        
        self.logger.info("ZeroMQ发布器已停止")


class FileEventProcessor:
    """文件事件处理器 - 处理文件事件队列"""
    
    def __init__(self, publisher: ZeroMQPublisher, file_types: Set[str], max_workers: int = 5):
        self.publisher = publisher
        self.file_types = file_types
        self.event_queue = Queue()
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.running = False
        self.processed_files = set()  # 防止重复处理
        
        self.logger = logging.getLogger('FileEventProcessor')
    
    def add_file_event(self, file_path: str, event_type: str):
        """添加文件事件到队列"""
        if not self.running:
            return
        
        # 检查文件类型
        if self.file_types and not self._is_supported_file(file_path):
            self.logger.debug(f"不支持的文件类型: {file_path}")
            return
        
        # 防止重复处理
        file_key = f"{file_path}:{os.path.getmtime(file_path) if os.path.exists(file_path) else 0}"
        if file_key in self.processed_files:
            return
        
        self.processed_files.add(file_key)
        self.event_queue.put((file_path, event_type, time.time()))
        self.logger.debug(f"文件事件已添加到队列: {file_path} ({event_type})")
    
    def _is_supported_file(self, file_path: str) -> bool:
        """检查是否为支持的文件类型"""
        if not self.file_types:
            return True
        
        file_ext = os.path.splitext(file_path)[1].lower()
        filename = os.path.basename(file_path).lower()
        
        for pattern in self.file_types:
            if pattern.startswith('*.'):
                if file_ext == pattern[1:]:
                    return True
            elif pattern in filename:
                return True
        
        return False
    
    def _process_file_event(self, file_path: str, event_type: str, event_time: float):
        """处理单个文件事件"""
        try:
            # 等待文件写入完成
            time.sleep(0.5)
            
            if not os.path.exists(file_path):
                self.logger.warning(f"文件不存在: {file_path}")
                return
            
            # 检查文件是否还在修改中
            current_mtime = os.path.getmtime(file_path)
            if time.time() - current_mtime < 1.0:
                self.logger.debug(f"文件可能还在写入中，稍后重试: {file_path}")
                time.sleep(1.0)
            
            # 发布文件
            success = self.publisher.publish_file(file_path)
            
            if success:
                self.logger.info(f"文件事件处理成功: {file_path} ({event_type})")
            else:
                self.logger.error(f"文件事件处理失败: {file_path} ({event_type})")
                
        except Exception as e:
            self.logger.error(f"处理文件事件异常 {file_path}: {e}")
    
    def start(self):
        """启动事件处理器"""
        self.running = True
        self.logger.info("文件事件处理器已启动")
        
        # 启动事件处理线程
        def process_events():
            while self.running:
                try:
                    file_path, event_type, event_time = self.event_queue.get(timeout=1.0)
                    
                    # 提交到线程池处理
                    self.executor.submit(
                        self._process_file_event, 
                        file_path, 
                        event_type, 
                        event_time
                    )
                    
                except Empty:
                    continue
                except Exception as e:
                    self.logger.error(f"处理事件队列异常: {e}")
        
        self.process_thread = threading.Thread(target=process_events, daemon=True)
        self.process_thread.start()
    
    def stop(self):
        """停止事件处理器"""
        self.running = False
        
        if hasattr(self, 'process_thread'):
            self.process_thread.join(timeout=5.0)
        
        self.executor.shutdown(wait=True)
        self.logger.info("文件事件处理器已停止")


class FileSystemWatcher:
    """文件系统监控器"""
    
    def __init__(self, watch_paths: List[str], event_processor: FileEventProcessor):
        self.watch_paths = watch_paths
        self.event_processor = event_processor
        self.observers = []
        self.running = False
        
        self.logger = logging.getLogger('FileSystemWatcher')
    
    class FileEventHandler(FileSystemEventHandler):
        """文件系统事件处理器"""
        
        def __init__(self, processor: FileEventProcessor):
            self.processor = processor
            self.logger = logging.getLogger('FileEventHandler')
        
        def on_created(self, event):
            if not event.is_directory:
                self.logger.debug(f"文件创建: {event.src_path}")
                self.processor.add_file_event(event.src_path, 'created')
        
        # def on_modified(self, event):
        #     if not event.is_directory:
        #         self.logger.debug(f"文件修改: {event.src_path}")
        #         self.processor.add_file_event(event.src_path, 'modified')
        
        # def on_moved(self, event):
        #     if not event.is_directory:
        #         self.logger.debug(f"文件移动: {event.src_path} -> {event.dest_path}")
        #         self.processor.add_file_event(event.dest_path, 'moved')
    
    def start(self):
        """启动文件系统监控"""
        try:
            self.running = True
            event_handler = self.FileEventHandler(self.event_processor)
            
            for watch_path in self.watch_paths:
                # 确保监控目录存在
                os.makedirs(watch_path, exist_ok=True)
                
                observer = Observer()
                observer.schedule(event_handler, watch_path, recursive=True)
                observer.start()
                
                self.observers.append(observer)
                self.logger.info(f"开始监控目录: {watch_path}")
            
            self.logger.info("文件系统监控已启动")
            
        except Exception as e:
            self.logger.error(f"启动文件系统监控失败: {e}")
            raise
    
    def stop(self):
        """停止文件系统监控"""
        self.running = False
        
        for observer in self.observers:
            observer.stop()
            observer.join()
        
        self.observers.clear()
        self.logger.info("文件系统监控已停止")


class FilePublishService:
    """文件发布服务主类"""
    
    def __init__(self, config_path: str = "config.ini"):
        self.config_path = config_path
        self.config = self._load_config(config_path)
        
        # 设置日志
        self._setup_logging()
        
        self.logger = logging.getLogger('FilePublishService')
        
        # 初始化组件
        self.publisher = None
        self.event_processor = None
        self.file_watcher = None
        self.running = False
        
        # 加载配置
        self._load_service_config()
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        config = configparser.ConfigParser()
        config.read(config_path, encoding='utf-8')
        
        result = {}
        for section in config.sections():
            result[section] = dict(config[section])
        
        return result
    
    def _setup_logging(self):
        """设置日志配置"""
        log_config = self.config.get('logging', {})
        
        logging.basicConfig(
            level=getattr(logging, log_config.get('level', 'INFO')),
            format=log_config.get('format', '[%(asctime)s] [%(name)s] %(message)s'),
            datefmt=log_config.get('date_format', '%Y-%m-%d %H:%M:%S')
        )
    
    def _load_service_config(self):
        """加载服务配置"""
        pub_config = self.config.get('file_publish', {})
        
        # 监控路径
        watch_paths_str = pub_config.get('watch_paths', './data/watch')
        self.watch_paths = [path.strip() for path in watch_paths_str.split(';')]
        
        # ZeroMQ发布地址
        self.zmq_publish_addr = pub_config.get('zmq_publish_addr', 'tcp://*:5555')
        self.topics = pub_config.get('topics','leo_data')
        # 文件类型过滤
        file_types_str = pub_config.get('file_types', '*.json')
        self.file_types = set(ft.strip() for ft in file_types_str.split(','))
        
        # 服务启用状态
        self.service_enabled = pub_config.get('file_publish_enable', 'true').lower() == 'true'
        
        self.logger.info(f"服务配置加载完成:")
        self.logger.info(f"  监控路径: {self.watch_paths}")
        self.logger.info(f"  发布地址: {self.zmq_publish_addr}")
        self.logger.info(f"  文件类型: {self.file_types}")
        self.logger.info(f"  服务启用: {self.service_enabled}")
    
    def initialize(self):
        """初始化服务组件"""
        try:
            if not self.service_enabled:
                self.logger.info("文件发布服务已禁用")
                return
            
            self.logger.info("初始化文件发布服务...")
            
            # 初始化ZeroMQ发布器
            self.publisher = ZeroMQPublisher(
                bind_address=self.zmq_publish_addr,
                topics=self.topics
            )
            
            # 初始化事件处理器
            self.event_processor = FileEventProcessor(
                publisher=self.publisher,
                file_types=self.file_types,
                max_workers=5
            )
            
            # 初始化文件系统监控器
            self.file_watcher = FileSystemWatcher(
                watch_paths=self.watch_paths,
                event_processor=self.event_processor
            )
            
            self.logger.info("文件发布服务初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化文件发布服务失败: {e}")
            raise
    
    def start(self):
        """启动服务"""
        try:
            if not self.service_enabled:
                self.logger.info("文件发布服务已禁用，跳过启动")
                return
            
            self.running = True
            self.logger.info("启动文件发布服务...")
            
            # 启动ZeroMQ发布器
            if not self.publisher.start():
                raise Exception("ZeroMQ发布器启动失败")
            
            # 启动事件处理器
            self.event_processor.start()
            
            # 启动文件系统监控
            self.file_watcher.start()
            
            self.logger.info("文件发布服务启动完成")
            
        except Exception as e:
            self.logger.error(f"启动文件发布服务失败: {e}")
            raise
    
    def stop(self):
        """停止服务"""
        try:
            self.running = False
            self.logger.info("停止文件发布服务...")
            
            # 停止文件系统监控
            if self.file_watcher:
                self.file_watcher.stop()
            
            # 停止事件处理器
            if self.event_processor:
                self.event_processor.stop()
            
            # 停止ZeroMQ发布器
            if self.publisher:
                self.publisher.stop()
            
            self.logger.info("文件发布服务已停止")
            
        except Exception as e:
            self.logger.error(f"停止文件发布服务失败: {e}")
        
    def publish_file_manually(self, file_path: str) -> bool:
        """手动发布文件"""
        if not self.running or not self.publisher:
            self.logger.error("服务未运行")
            return False
        
        return self.publisher.publish_file(file_path)


def main():
    """主函数 - 独立运行文件发布服务"""
    service = FilePublishService()
    
    def signal_handler(signum, frame):
        print(f"\n收到信号 {signum}，正在关闭文件发布服务...")
        service.stop()
        sys.exit(0)
    
    # 设置信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 初始化并启动服务
        service.initialize()
        service.start()
        
        print("文件发布服务正在运行，按 Ctrl+C 退出...")
        print(f"监控路径: {service.watch_paths}")
        print(f"发布地址: {service.zmq_publish_addr}")
        
        # 保持运行
        while service.running:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n收到中断信号，正在关闭服务...")
    except Exception as e:
        print(f"文件发布服务运行失败: {e}")
        sys.exit(1)
    finally:
        service.stop()


if __name__ == "__main__":
    main()
