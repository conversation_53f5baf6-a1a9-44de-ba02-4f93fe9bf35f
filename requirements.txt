# 分布式文件处理系统依赖包
# Python 3.10.18+ 兼容

# Web框架和API服务
fastapi>=0.104.0
uvicorn>=0.24.0
pydantic>=2.0.0

# 消息队列和通信
pyzmq>=25.0.0

# 文件系统监控
watchdog>=3.0.0

# 异步文件操作
aiofiles>=23.0.0

# HTTP请求处理
python-multipart>=0.0.6

# 数据库相关
# SQLite是Python内置的，无需额外安装

# 并发和异步支持
# asyncio是Python内置的，无需额外安装

# 日志和配置
# logging和configparser是Python内置的，无需额外安装

# 文件处理和数据解析
# json和csv是Python内置的，无需额外安装

# 系统和路径操作
# os, sys, pathlib是Python内置的，无需额外安装

# 线程和进程
# threading, concurrent.futures是Python内置的，无需额外安装

# 时间和UUID
# time, uuid, datetime是Python内置的，无需额外安装

# 信号处理
# signal是Python内置的，无需额外安装

# 数学和哈希
# hashlib是Python内置的，无需额外安装
