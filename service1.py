#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一启动模块 - 直接启动四个服务模块，支持ctrl+c退出
安全释放资源

创建时间: 2025-01-25
"""

import asyncio
import configparser
import logging
import signal
import sys
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, Any, List, Optional

# 导入四个核心服务模块
from database import DatabaseService
from file_publish import FilePublishService
from api import APIService
from subscribe import SubscribeService


class UnifiedServiceManager:
    """统一服务管理器"""
    
    def __init__(self, config_path: str = "config.ini"):
        self.config_path = config_path
        self.services = {}
        self.running = False
        self.executor = ThreadPoolExecutor(max_workers=10)
        self.service_enabled = {
            'database': True,
            'file_publish': True,
            'api': True,
            'subscribe': True,
        }
        
        # 设置日志
        self._setup_logging()
        self.logger = logging.getLogger('UnifiedServiceManager')
        
        # 读取配置开关
        self._load_enable_flags()
        
        # 服务启动顺序（重要：数据库服务需要先启动）
        self.service_order = [
            'database',
            'file_publish', 
            'subscribe',
            'api'
        ]
        
        # 信号处理标志
        self._shutdown_requested = False
    
    def _setup_logging(self):
        """设置日志配置"""
        logging.basicConfig(
            level=logging.INFO,
            format='[%(asctime)s] [%(name)s] [%(levelname)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 设置各模块日志级别
        logging.getLogger('DatabaseService').setLevel(logging.INFO)
        logging.getLogger('FilePublishService').setLevel(logging.INFO)
        logging.getLogger('APIService').setLevel(logging.INFO)
        logging.getLogger('SubscribeService').setLevel(logging.INFO)
    
    def _load_enable_flags(self):
        """从配置文件读取各服务启用开关"""
        try:
            cfg = configparser.ConfigParser()
            cfg.read(self.config_path, encoding='utf-8')
            
            def get_bool(section: str, key: str, default: bool) -> bool:
                try:
                    return cfg.get(section, key, fallback=str(default)).strip().lower() == 'true'
                except Exception:
                    return default
            
            self.service_enabled['database'] = get_bool('database', 'database_enable', True)
            self.service_enabled['file_publish'] = get_bool('file_publish', 'file_publish_enable', True)
            self.service_enabled['api'] = get_bool('api', 'api_enable', True)
            self.service_enabled['subscribe'] = get_bool('subscribe', 'subscribe_enable', True)
            
            self.logger.info("服务启用配置: "
                             f"database={self.service_enabled['database']}, "
                             f"file_publish={self.service_enabled['file_publish']}, "
                             f"api={self.service_enabled['api']}, "
                             f"subscribe={self.service_enabled['subscribe']}")
        except Exception as e:
            self.logger.warning(f"读取服务启用配置失败，采用默认值: {e}")
    
    async def initialize_services(self):
        """初始化所有服务"""
        try:
            self.logger.info("=" * 60)
            self.logger.info("🚀 开始初始化分布式文件处理系统")
            self.logger.info("=" * 60)
            
            # 初始化数据库服务
            if self.service_enabled.get('database', True):
                self.logger.info("📊 初始化数据库服务...")
                self.services['database'] = DatabaseService(self.config_path)
                self.services['database'].initialize()
            else:
                self.logger.info("⏸️ 数据库服务已禁用，跳过初始化")
            
            # 初始化文件发布服务
            if self.service_enabled.get('file_publish', True):
                self.logger.info("📤 初始化文件发布服务...")
                self.services['file_publish'] = FilePublishService(self.config_path)
                self.services['file_publish'].initialize()
            else:
                self.logger.info("⏸️ 文件发布服务已禁用，跳过初始化")
            
            # 初始化订阅服务
            if self.service_enabled.get('subscribe', True):
                self.logger.info("📥 初始化订阅服务...")
                self.services['subscribe'] = SubscribeService(self.config_path)
                self.services['subscribe'].initialize()
            else:
                self.logger.info("⏸️ 订阅服务已禁用，跳过初始化")
            
            # 初始化API服务
            if self.service_enabled.get('api', True):
                self.logger.info("🌐 初始化API服务...")
                self.services['api'] = APIService(self.config_path)
                await self.services['api'].initialize()
            else:
                self.logger.info("⏸️ API服务已禁用，跳过初始化")
            
            self.logger.info("✅ 所有服务初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 服务初始化失败: {e}")
            raise
    
    async def start_all_services(self):
        """启动所有服务"""
        try:
            self.running = True
            self.logger.info("=" * 60)
            self.logger.info("🎯 开始启动所有服务")
            self.logger.info("=" * 60)
            
            # 按顺序启动服务
            for service_name in self.service_order:
                if service_name in self.services:
                    await self._start_single_service(service_name)
            
            self.logger.info("=" * 60)
            self.logger.info("🎉 所有服务启动完成！")
            self._print_service_status()
            self.logger.info("=" * 60)
            self.logger.info("💡 按 Ctrl+C 可安全退出所有服务")
            self.logger.info("=" * 60)
            
        except Exception as e:
            self.logger.error(f"❌ 启动服务失败: {e}")
            await self.stop_all_services()
            raise
    
    async def _start_single_service(self, service_name: str):
        """启动单个服务"""
        try:
            service = self.services[service_name]
            
            if service_name == 'api':
                # API服务需要异步启动
                self.logger.info(f"🌐 启动{service_name}服务...")
                # 在后台任务中启动API服务
                asyncio.create_task(self._run_api_service())
                # 给API服务一些时间启动
                await asyncio.sleep(2)
                self.logger.info(f"✅ {service_name}服务启动完成")
            else:
                # 其他服务同步启动
                self.logger.info(f"🔧 启动{service_name}服务...")
                service.start()
                self.logger.info(f"✅ {service_name}服务启动完成")
                
        except Exception as e:
            self.logger.error(f"❌ 启动{service_name}服务失败: {e}")
            raise
    
    async def _run_api_service(self):
        """在后台运行API服务"""
        try:
            api_service = self.services['api']
            await api_service.start()
        except Exception as e:
            self.logger.error(f"API服务运行异常: {e}")
    
    def _print_service_status(self):
        """打印服务状态"""
        
        # 打印访问信息
        if 'api' in self.services:
            api_service = self.services['api']
            if hasattr(api_service, 'host') and hasattr(api_service, 'port'):
                self.logger.info(f"🌐 API服务地址: http://{api_service.host}:{api_service.port}")
                self.logger.info(f"📚 API文档地址: http://{api_service.host}:{api_service.port}/docs")
    
    async def stop_all_services(self):
        """停止所有服务"""
        try:
            if self._shutdown_requested:
                return
            
            self._shutdown_requested = True
            self.running = False
            
            self.logger.info("=" * 60)
            self.logger.info("🛑 开始停止所有服务...")
            self.logger.info("=" * 60)
            
            # 按相反顺序停止服务
            for service_name in reversed(self.service_order):
                if service_name in self.services:
                    await self._stop_single_service(service_name)
            
            # 关闭线程池
            self.executor.shutdown(wait=True)
            
            self.logger.info("=" * 60)
            self.logger.info("✅ 所有服务已安全停止")
            self.logger.info("=" * 60)
            
        except Exception as e:
            self.logger.error(f"❌ 停止服务时出错: {e}")
    
    async def _stop_single_service(self, service_name: str):
        """停止单个服务"""
        try:
            service = self.services[service_name]
            self.logger.info(f"🔧 停止{service_name}服务...")
            
            if service_name == 'api' and hasattr(service, 'stop'):
                await service.stop()
            elif hasattr(service, 'stop'):
                service.stop()
            
            self.logger.info(f"✅ {service_name}服务已停止")
            
        except Exception as e:
            self.logger.error(f"❌ 停止{service_name}服务失败: {e}")
    
    def get_overall_status(self) -> Dict[str, Any]:
        """获取整体状态"""
        status = {
            'running': self.running,
            'services': {}
        }
        
        for service_name, service in self.services.items():
            if hasattr(service, 'get_status'):
                status['services'][service_name] = service.get_status()
            else:
                status['services'][service_name] = {'available': False}
        
        return status
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self.running:
                return False
            
            # 检查各服务状态
            for service_name, service in self.services.items():
                if hasattr(service, 'get_status'):
                    status = service.get_status()
                    if not status.get('running', False):
                        self.logger.warning(f"服务 {service_name} 未运行")
                        return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
            return False


def setup_signal_handlers(manager: UnifiedServiceManager):
    """设置信号处理器"""
    def signal_handler(signum, frame):
        print(f"\n🛑 收到信号 {signum}，正在安全关闭所有服务...")
        # 不在信号处理器里操作事件循环，改为通知主循环退出
        try:
            manager.running = False
        except Exception as e:
            print(f"❌ 设置关闭标志时出错: {e}")
        finally:
            print("👋 再见！")
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # 终止信号


async def main():
    """主函数"""
    print("🚀 分布式文件处理系统启动器")
    print("=" * 60)
    
    manager = UnifiedServiceManager()
    
    # 设置信号处理器
    setup_signal_handlers(manager)
    
    try:
        # 初始化所有服务
        await manager.initialize_services()
        
        # 启动所有服务
        await manager.start_all_services()
        
        # 保持运行状态
        while manager.running:
            await asyncio.sleep(1)
            
            # 定期健康检查（每30秒）
            if int(time.time()) % 30 == 0:
                health = await manager.health_check()
                if not health:
                    manager.logger.warning("⚠️ 健康检查失败，某些服务可能异常")
        
    except KeyboardInterrupt:
        print("\n🛑 收到中断信号，正在关闭服务...")
    except Exception as e:
        print(f"❌ 系统运行失败: {e}")
        manager.logger.error(f"系统运行失败: {e}")
        sys.exit(1)
    finally:
        await manager.stop_all_services()


if __name__ == "__main__":
    try:
        # Windows系统设置事件循环策略
        if sys.platform == 'win32':
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
        # 运行主程序
        asyncio.run(main())
        
    except KeyboardInterrupt:
        print("\n👋 服务已终止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)
