#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双服务启动脚本 - 同时启动API服务和搜索服务
演示两个服务如何并行运行在不同端口

创建时间: 2025-01-25
"""

import asyncio
import signal
import sys
import logging
from concurrent.futures import ThreadPoolExecutor
import threading
import time

# 导入服务类
from api import APIService
from search import SearchService


class DualServiceManager:
    """双服务管理器"""
    
    def __init__(self):
        self.api_service = None
        self.search_service = None
        self.running = False
        self.logger = logging.getLogger('DualServiceManager')
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    async def initialize_services(self):
        """初始化两个服务"""
        try:
            self.logger.info("初始化API服务和搜索服务...")
            
            # 初始化API服务
            self.api_service = APIService()
            await self.api_service.initialize()
            
            # 初始化搜索服务
            self.search_service = SearchService()
            await self.search_service.initialize()
            
            self.logger.info("两个服务初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化服务失败: {e}")
            raise
    
    async def start_services(self):
        """启动两个服务"""
        try:
            self.running = True
            
            self.logger.info("=" * 60)
            self.logger.info("🚀 启动双服务系统")
            self.logger.info("=" * 60)
            
            # 创建任务列表
            tasks = []
            
            # 启动API服务
            if self.api_service and self.api_service.service_enabled:
                api_task = asyncio.create_task(self.api_service.start())
                tasks.append(api_task)
                self.logger.info(f"📡 API服务: http://{self.api_service.host}:{self.api_service.port}")
                self.logger.info(f"📖 API文档: http://{self.api_service.host}:{self.api_service.port}/docs")
            
            # 启动搜索服务
            if self.search_service and self.search_service.service_enabled:
                search_task = asyncio.create_task(self.search_service.start())
                tasks.append(search_task)
                self.logger.info(f"🔍 搜索服务: http://{self.search_service.host}:{self.search_service.port}")
                self.logger.info(f"📖 搜索文档: http://{self.search_service.host}:{self.search_service.port}/docs")
                self.logger.info(f"📁 文件保存: {self.search_service.save_directory}")
            
            if not tasks:
                self.logger.warning("没有启用的服务")
                return
            
            self.logger.info("=" * 60)
            self.logger.info("✅ 双服务系统启动完成")
            self.logger.info("💡 按 Ctrl+C 可安全退出所有服务")
            self.logger.info("=" * 60)
            
            # 等待所有任务完成
            await asyncio.gather(*tasks, return_exceptions=True)
            
        except Exception as e:
            self.logger.error(f"启动服务失败: {e}")
            await self.stop_services()
            raise
    
    async def stop_services(self):
        """停止两个服务"""
        try:
            if not self.running:
                return
            
            self.running = False
            
            self.logger.info("=" * 60)
            self.logger.info("🛑 停止双服务系统")
            self.logger.info("=" * 60)
            
            # 停止搜索服务
            if self.search_service:
                self.logger.info("停止搜索服务...")
                await self.search_service.stop()
            
            # 停止API服务
            if self.api_service:
                self.logger.info("停止API服务...")
                await self.api_service.stop()
            
            self.logger.info("=" * 60)
            self.logger.info("✅ 双服务系统已安全停止")
            self.logger.info("=" * 60)
            
        except Exception as e:
            self.logger.error(f"停止服务时出错: {e}")
    
    def print_service_comparison(self):
        """打印服务对比信息"""
        print("\n" + "=" * 80)
        print("📊 服务对比信息")
        print("=" * 80)
        
        comparison_table = [
            ["特性", "API服务 (api.py)", "搜索服务 (search.py)"],
            ["-" * 20, "-" * 25, "-" * 30],
            ["端口", "8000", "8001"],
            ["ZeroMQ支持", "✅ 是", "❌ 否"],
            ["文件保存", "❌ 否", "✅ 是"],
            ["消息发布", "✅ 是", "❌ 否"],
            ["配置节", "[api]", "[search]"],
            ["用途", "实时消息发布", "结果文件保存"],
        ]
        
        for row in comparison_table:
            print(f"{row[0]:<20} {row[1]:<25} {row[2]:<30}")
        
        print("\n💡 使用建议:")
        print("  - API服务: 适用于需要ZeroMQ消息发布的场景")
        print("  - 搜索服务: 适用于需要保存查询结果文件的场景")
        print("  - 可以同时运行两个服务，满足不同需求")


def setup_signal_handlers(manager: DualServiceManager):
    """设置信号处理器"""
    def signal_handler(signum, frame):
        print(f"\n🛑 收到信号 {signum}，正在安全关闭所有服务...")
        manager.running = False
        # 创建新的事件循环来处理停止操作
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(manager.stop_services())
            loop.close()
        except Exception as e:
            print(f"❌ 停止服务时出错: {e}")
        finally:
            print("👋 再见！")
            sys.exit(0)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # 终止信号


async def main():
    """主函数"""
    print("🚀 双服务启动器")
    print("同时启动API服务和搜索服务")
    
    manager = DualServiceManager()
    
    # 设置信号处理器
    setup_signal_handlers(manager)
    
    try:
        # 打印服务对比信息
        manager.print_service_comparison()
        
        # 初始化服务
        await manager.initialize_services()
        
        # 启动服务
        await manager.start_services()
        
        # 保持运行状态
        while manager.running:
            await asyncio.sleep(1)
        
    except KeyboardInterrupt:
        print("\n🛑 收到中断信号，正在关闭服务...")
    except Exception as e:
        print(f"❌ 系统运行失败: {e}")
        manager.logger.error(f"系统运行失败: {e}")
    finally:
        await manager.stop_services()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 程序运行失败: {e}")
        sys.exit(1)
