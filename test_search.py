#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索服务测试脚本 - 验证search.py模块的功能
测试API接口、文件保存功能和数据库查询

创建时间: 2025-01-25
"""

import asyncio
import json
import os
import requests
import time
from datetime import datetime


class SearchServiceTester:
    """搜索服务测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.test_results = []
    
    def test_api_endpoint(self, endpoint: str, params: dict = None, description: str = ""):
        """测试API端点"""
        try:
            url = f"{self.base_url}{endpoint}"
            print(f"\n🧪 测试: {description}")
            print(f"URL: {url}")
            if params:
                print(f"参数: {params}")
            
            start_time = time.time()
            response = requests.get(url, params=params, timeout=30)
            end_time = time.time()
            
            print(f"状态码: {response.status_code}")
            print(f"响应时间: {end_time - start_time:.2f}秒")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 成功: {data.get('message', '无消息')}")
                
                # 检查是否有保存的文件路径
                if 'saved_file' in data:
                    saved_file = data['saved_file']
                    print(f"📁 保存文件: {saved_file}")
                    
                    # 检查文件是否存在
                    if os.path.exists(saved_file):
                        file_size = os.path.getsize(saved_file)
                        print(f"📄 文件大小: {file_size} 字节")
                        
                        # 验证文件内容是否为有效JSON
                        try:
                            with open(saved_file, 'r', encoding='utf-8') as f:
                                json.load(f)
                            print("✅ JSON文件格式有效")
                        except json.JSONDecodeError:
                            print("❌ JSON文件格式无效")
                    else:
                        print("❌ 保存的文件不存在")
                
                self.test_results.append({
                    "endpoint": endpoint,
                    "description": description,
                    "status": "SUCCESS",
                    "response_time": end_time - start_time,
                    "data_count": len(data.get('data', [])) if isinstance(data.get('data'), list) else 1
                })
                return True
            else:
                print(f"❌ 失败: {response.text}")
                self.test_results.append({
                    "endpoint": endpoint,
                    "description": description,
                    "status": "FAILED",
                    "error": response.text
                })
                return False
                
        except Exception as e:
            print(f"❌ 异常: {e}")
            self.test_results.append({
                "endpoint": endpoint,
                "description": description,
                "status": "ERROR",
                "error": str(e)
            })
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("🚀 开始搜索服务API测试")
        print("=" * 60)
        
        # 测试根路径
        self.test_api_endpoint("/", description="根路径健康检查")
        
        # 测试位置转换查询
        self.test_api_endpoint(
            "/tasks/position/",
            params={"device_id": "SAT001", "timestamp": "1640995200000"},
            description="位置转换查询"
        )
        
        # 测试融合定位查询
        self.test_api_endpoint(
            "/tasks/fusion/",
            params={"device_id": "SAT001", "timestamp": "1640995200000"},
            description="融合定位查询"
        )
        
        # 测试意图分析查询
        self.test_api_endpoint(
            "/tasks/intention/",
            params={"style": "7"},
            description="意图分析查询"
        )
        
        # 测试电侦智能查询
        self.test_api_endpoint(
            "/tasks/XDZN/",
            params={"device_id": "SAT001", "timestamp": "1640995200000"},
            description="电侦智能查询"
        )
        
        # 测试雷达智能查询
        self.test_api_endpoint(
            "/tasks/BKZN/",
            params={"device_id": "SAT001", "timestamp": "1640995200000"},
            description="雷达智能查询"
        )
        
        # 测试原始数据查询
        self.test_api_endpoint(
            "/data/origional/",
            params={"timestamps": "[1640995200000]"},
            description="原始数据查询"
        )
        
        # 测试特征数据查询
        self.test_api_endpoint(
            "/data/feature/",
            params={"timestamps": "[1640995200000]"},
            description="特征数据查询"
        )
        
        # 测试决策数据查询
        self.test_api_endpoint(
            "/data/decision/",
            params={"timestamps": "[1640995200000]"},
            description="决策数据查询"
        )
        
        # 打印测试总结
        self.print_test_summary()
    
    def print_test_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("📊 测试总结")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        successful_tests = len([r for r in self.test_results if r['status'] == 'SUCCESS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAILED'])
        error_tests = len([r for r in self.test_results if r['status'] == 'ERROR'])
        
        print(f"总测试数: {total_tests}")
        print(f"✅ 成功: {successful_tests}")
        print(f"❌ 失败: {failed_tests}")
        print(f"⚠️  错误: {error_tests}")
        print(f"成功率: {successful_tests/total_tests*100:.1f}%")
        
        if successful_tests > 0:
            avg_response_time = sum([r.get('response_time', 0) for r in self.test_results if r['status'] == 'SUCCESS']) / successful_tests
            print(f"平均响应时间: {avg_response_time:.2f}秒")
        
        # 详细结果
        print("\n📋 详细结果:")
        for result in self.test_results:
            status_icon = "✅" if result['status'] == 'SUCCESS' else "❌"
            print(f"{status_icon} {result['description']}: {result['status']}")
            if result['status'] == 'SUCCESS' and 'response_time' in result:
                print(f"   响应时间: {result['response_time']:.2f}秒")
                if 'data_count' in result:
                    print(f"   数据条数: {result['data_count']}")
            elif result['status'] != 'SUCCESS':
                print(f"   错误: {result.get('error', '未知错误')}")


def check_service_status(base_url: str = "http://localhost:8001"):
    """检查服务状态"""
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ 搜索服务正在运行")
            return True
        else:
            print(f"❌ 搜索服务响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到搜索服务，请确保服务已启动")
        return False
    except Exception as e:
        print(f"❌ 检查服务状态时出错: {e}")
        return False


def create_test_data_directory():
    """创建测试数据目录"""
    test_dir = "./search_results"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
        print(f"📁 创建测试目录: {test_dir}")
    else:
        print(f"📁 测试目录已存在: {test_dir}")


def cleanup_test_files():
    """清理测试文件"""
    test_dir = "./search_results"
    if os.path.exists(test_dir):
        try:
            import shutil
            shutil.rmtree(test_dir)
            print(f"🧹 清理测试目录: {test_dir}")
        except Exception as e:
            print(f"⚠️ 清理测试目录失败: {e}")


def main():
    """主函数"""
    print("🔍 搜索服务测试工具")
    print("=" * 60)
    
    # 检查服务状态
    if not check_service_status():
        print("\n💡 启动搜索服务的方法:")
        print("   python search.py")
        return
    
    # 创建测试目录
    create_test_data_directory()
    
    # 运行测试
    tester = SearchServiceTester()
    tester.run_all_tests()
    
    # 询问是否清理测试文件
    print("\n" + "=" * 60)
    cleanup = input("是否清理测试生成的文件? (y/N): ").lower().strip()
    if cleanup == 'y':
        cleanup_test_files()
    else:
        print("📁 测试文件保留在 ./search_results 目录中")


if __name__ == "__main__":
    main()
