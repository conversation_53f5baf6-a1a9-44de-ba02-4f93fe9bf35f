#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证store.py修改的脚本 - 检查数据处理逻辑是否与database.py一致
不需要运行完整的store.py，只验证关键修改

创建时间: 2025-01-25
"""

import re
import os


def check_store_modifications():
    """检查store.py的修改是否正确"""
    print("=" * 60)
    print("🔍 验证store.py修改")
    print("=" * 60)
    
    if not os.path.exists('store.py'):
        print("❌ store.py文件不存在")
        return False
    
    with open('store.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = []
    
    # 检查1: 数据库表结构是否使用正确的表名
    if 'origional_table' in content:
        checks.append(("✅", "使用正确的表名 'origional_table'"))
    else:
        checks.append(("❌", "未找到正确的表名 'origional_table'"))
    
    if 'feature_table' in content:
        checks.append(("✅", "使用正确的表名 'feature_table'"))
    else:
        checks.append(("❌", "未找到正确的表名 'feature_table'"))
    
    if 'truth_table' in content:
        checks.append(("✅", "使用正确的表名 'truth_table'"))
    else:
        checks.append(("❌", "未找到正确的表名 'truth_table'"))
    
    if 'decision_table' in content:
        checks.append(("✅", "使用正确的表名 'decision_table'"))
    else:
        checks.append(("❌", "未找到正确的表名 'decision_table'"))
    
    # 检查2: 是否包含database.py的文件解析方法
    if '_parse_filename' in content:
        checks.append(("✅", "包含文件名解析方法 '_parse_filename'"))
    else:
        checks.append(("❌", "缺少文件名解析方法 '_parse_filename'"))
    
    if '_is_origin_data' in content:
        checks.append(("✅", "包含源数据判断方法 '_is_origin_data'"))
    else:
        checks.append(("❌", "缺少源数据判断方法 '_is_origin_data'"))
    
    if '_parse_and_store_data' in content:
        checks.append(("✅", "包含数据解析存储方法 '_parse_and_store_data'"))
    else:
        checks.append(("❌", "缺少数据解析存储方法 '_parse_and_store_data'"))
    
    # 检查3: 是否包含各种特征数据存储方法
    feature_methods = [
        '_store_origin_data',
        '_store_feature_data_BHSF',
        '_store_feature_data_WZZH', 
        '_store_feature_data_RHDW',
        '_store_feature_data_XDSF',
        '_store_feature_data_BKSF',
        '_store_decision_data'
    ]
    
    for method in feature_methods:
        if method in content:
            checks.append(("✅", f"包含方法 '{method}'"))
        else:
            checks.append(("❌", f"缺少方法 '{method}'"))
    
    # 检查4: 是否包含数据库插入辅助方法
    insert_methods = [
        '_insert_origin_data',
        '_insert_truth_data', 
        '_insert_feature_data'
    ]
    
    for method in insert_methods:
        if method in content:
            checks.append(("✅", f"包含插入方法 '{method}'"))
        else:
            checks.append(("❌", f"缺少插入方法 '{method}'"))
    
    # 检查5: 是否包含类型映射
    if 'device_type_map' in content:
        checks.append(("✅", "包含设备类型映射"))
    else:
        checks.append(("❌", "缺少设备类型映射"))
    
    if 'target_type_map' in content:
        checks.append(("✅", "包含目标类型映射"))
    else:
        checks.append(("❌", "缺少目标类型映射"))
    
    # 检查6: 是否移除了旧的数据存储方法
    if '_store_original_data' not in content and '_store_feature_data' not in content:
        checks.append(("✅", "已移除旧的数据存储方法"))
    else:
        checks.append(("❌", "仍包含旧的数据存储方法"))
    
    # 检查7: 是否包含dateutil导入
    if 'from dateutil import parser' in content:
        checks.append(("✅", "包含dateutil导入"))
    else:
        checks.append(("❌", "缺少dateutil导入"))
    
    # 打印检查结果
    print("\n📋 检查结果:")
    success_count = 0
    total_count = len(checks)
    
    for status, message in checks:
        print(f"{status} {message}")
        if status == "✅":
            success_count += 1
    
    print(f"\n📊 总结:")
    print(f"总检查项: {total_count}")
    print(f"通过项: {success_count}")
    print(f"失败项: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("\n🎉 所有检查项都通过！store.py修改成功！")
        return True
    else:
        print(f"\n⚠️ 有 {total_count - success_count} 项检查未通过，需要进一步修改")
        return False


def check_database_compatibility():
    """检查与database.py的兼容性"""
    print("\n" + "=" * 60)
    print("🔗 检查与database.py的兼容性")
    print("=" * 60)
    
    if not os.path.exists('database.py'):
        print("❌ database.py文件不存在，无法进行兼容性检查")
        return False
    
    if not os.path.exists('store.py'):
        print("❌ store.py文件不存在，无法进行兼容性检查")
        return False
    
    # 读取两个文件
    with open('database.py', 'r', encoding='utf-8') as f:
        db_content = f.read()
    
    with open('store.py', 'r', encoding='utf-8') as f:
        store_content = f.read()
    
    compatibility_checks = []
    
    # 检查表结构定义是否一致
    db_tables = re.findall(r'CREATE TABLE IF NOT EXISTS (\w+)', db_content)
    store_tables = re.findall(r'CREATE TABLE IF NOT EXISTS (\w+)', store_content)
    
    for table in db_tables:
        if table in store_tables:
            compatibility_checks.append(("✅", f"表 '{table}' 在两个文件中都存在"))
        else:
            compatibility_checks.append(("❌", f"表 '{table}' 在store.py中缺失"))
    
    # 检查关键方法是否存在
    key_methods = [
        '_parse_filename',
        '_is_origin_data', 
        '_parse_and_store_data'
    ]
    
    for method in key_methods:
        if method in db_content and method in store_content:
            compatibility_checks.append(("✅", f"方法 '{method}' 在两个文件中都存在"))
        elif method in db_content:
            compatibility_checks.append(("❌", f"方法 '{method}' 在store.py中缺失"))
    
    # 检查类型映射是否一致
    if 'device_type_map' in db_content and 'device_type_map' in store_content:
        compatibility_checks.append(("✅", "设备类型映射在两个文件中都存在"))
    
    if 'target_type_map' in db_content and 'target_type_map' in store_content:
        compatibility_checks.append(("✅", "目标类型映射在两个文件中都存在"))
    
    # 打印兼容性检查结果
    print("\n📋 兼容性检查结果:")
    success_count = 0
    total_count = len(compatibility_checks)
    
    for status, message in compatibility_checks:
        print(f"{status} {message}")
        if status == "✅":
            success_count += 1
    
    print(f"\n📊 兼容性总结:")
    print(f"总检查项: {total_count}")
    print(f"兼容项: {success_count}")
    print(f"不兼容项: {total_count - success_count}")
    print(f"兼容率: {success_count/total_count*100:.1f}%")
    
    return success_count == total_count


def generate_modification_summary():
    """生成修改总结"""
    print("\n" + "=" * 60)
    print("📝 修改总结")
    print("=" * 60)
    
    modifications = [
        "1. 数据库表结构",
        "   - 使用与database.py一致的表名：origional_table, feature_table, truth_table, decision_table",
        "   - 保持相同的字段定义和约束",
        "",
        "2. 文件解析逻辑",
        "   - 替换为database.py中的_parse_filename方法",
        "   - 使用相同的文件名格式：卫星id_任务类型_时间戳.ext",
        "   - 保持相同的时间戳处理逻辑",
        "",
        "3. 数据处理方法",
        "   - _process_message: 使用database.py的处理流程",
        "   - _parse_and_store_data: 根据任务类型分发数据",
        "   - _store_origin_data: 处理原始数据和真值数据",
        "   - _store_feature_data_*: 处理各种特征数据",
        "",
        "4. 类型映射",
        "   - device_type_map: 设备类型映射",
        "   - equip_type_map: 设备类型映射", 
        "   - target_type_map: 目标类型映射",
        "",
        "5. 数据库操作",
        "   - _insert_origin_data: 插入原始数据",
        "   - _insert_truth_data: 插入真值数据",
        "   - _insert_feature_data: 插入特征数据",
        "",
        "6. 保持的架构",
        "   - 三线程架构不变",
        "   - 消息队列流向不变",
        "   - 只修改消费者线程的数据处理部分"
    ]
    
    for line in modifications:
        print(line)


def main():
    """主函数"""
    print("🔍 store.py修改验证工具")
    
    # 检查store.py修改
    store_ok = check_store_modifications()
    
    # 检查与database.py的兼容性
    compat_ok = check_database_compatibility()
    
    # 生成修改总结
    generate_modification_summary()
    
    # 最终结果
    print("\n" + "=" * 60)
    print("🎯 最终结果")
    print("=" * 60)
    
    if store_ok and compat_ok:
        print("🎉 store.py修改完全成功！")
        print("✅ 所有检查项都通过")
        print("✅ 与database.py完全兼容")
        print("✅ 可以安全使用新的数据处理逻辑")
    elif store_ok:
        print("⚠️ store.py修改基本成功，但存在兼容性问题")
        print("✅ 基本功能检查通过")
        print("❌ 与database.py存在兼容性问题")
    else:
        print("❌ store.py修改不完整")
        print("❌ 基本功能检查未通过")
        print("❌ 需要进一步修改")


if __name__ == "__main__":
    main()
